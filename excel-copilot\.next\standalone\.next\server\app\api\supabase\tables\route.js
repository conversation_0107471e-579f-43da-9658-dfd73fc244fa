"use strict";(()=>{var e={};e.id=5015,e.ids=[5015],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},19623:(e,a,t)=>{t.r(a),t.d(a,{originalPathname:()=>g,patchFetch:()=>E,requestAsyncStorage:()=>R,routeModule:()=>S,serverHooks:()=>f,staticGenerationAsyncStorage:()=>x});var r={};t.r(r),t.d(r,{GET:()=>m,POST:()=>b,dynamic:()=>d,runtime:()=>p});var s=t(49303),o=t(88716),n=t(60670),i=t(87070),c=t(43895),u=t(57530),l=t(62091),_=t(82840);let d="force-dynamic",p="nodejs";async function m(e){try{let a=await (0,l.VF)(e,new i.NextResponse);if(a)return a;let t=process.env.SUPABASE_SERVICE_ROLE_KEY,r=process.env.SUPABASE_URL;if(!t||!r)return _.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let s=new u.$({serviceRoleKey:t,projectUrl:r}),{searchParams:o}=new URL(e.url),n="true"===o.get("details"),d=parseInt(o.get("limit")||"50"),p=o.get("sort")||"size",m=await s.getDatabaseSummary(),b=[...m.tables];switch(p){case"name":b.sort((e,a)=>e.name.localeCompare(a.name));break;case"rows":b.sort((e,a)=>(a.row_count||0)-(e.row_count||0));break;default:b.sort((e,a)=>(a.bytes||0)-(e.bytes||0))}let S=b.slice(0,d),R={summary:{totalTables:m.totalTables,totalSize:m.totalSize,largestTable:m.largestTables[0]?{name:m.largestTables[0].name,size:m.largestTables[0].size,schema:m.largestTables[0].schema}:null},tables:S.map(e=>({name:e.name,schema:e.schema,size:e.size||"0 Bytes",bytes:e.bytes||0,...n&&{rls_enabled:e.rls_enabled,rls_forced:e.rls_forced,row_count:e.row_count||0,dead_row_count:e.dead_row_count||0,seq_scan_count:e.seq_scan_count||0,idx_scan_count:e.idx_scan_count||0}})),pagination:{limit:d,total:m.totalTables,showing:S.length,hasMore:m.totalTables>d},filters:{sortBy:p,includeDetails:n},timestamp:new Date().toISOString()};return c.kg.info("Tabelas Supabase obtidas com sucesso",{totalTables:m.totalTables,showing:S.length,sortBy:p}),_.R.success(R)}catch(e){if(c.kg.error("Erro ao obter tabelas do Supabase",{error:e}),e instanceof Error)return _.R.error(`Erro ao acessar banco de dados: ${e.message}`,"SUPABASE_DATABASE_ERROR",500);return _.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function b(e){try{let a=await (0,l.VF)(e,new i.NextResponse);if(a)return a;let t=process.env.SUPABASE_SERVICE_ROLE_KEY,r=process.env.SUPABASE_URL;if(!t||!r)return _.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let{action:s,tableName:o,options:n}=await e.json(),c=new u.$({serviceRoleKey:t,projectUrl:r});switch(s){case"analyze":{if(!o)return _.R.error("Nome da tabela \xe9 obrigat\xf3rio para an\xe1lise","MISSING_TABLE_NAME",400);let e=(await c.getDatabaseSummary()).tables.find(e=>e.name===o);if(!e)return _.R.error(`Tabela '${o}' n\xe3o encontrada`,"TABLE_NOT_FOUND",404);let a={table:{name:e.name,schema:e.schema,size:e.size,bytes:e.bytes,row_count:e.row_count||0,dead_row_count:e.dead_row_count||0},performance:{seq_scan_count:e.seq_scan_count||0,seq_tup_read:e.seq_tup_read||0,idx_scan_count:e.idx_scan_count||0,idx_tup_fetch:e.idx_tup_fetch||0,scan_efficiency:e.idx_scan_count&&e.seq_scan_count?e.idx_scan_count/(e.idx_scan_count+e.seq_scan_count)*100:0},security:{rls_enabled:e.rls_enabled||!1,rls_forced:e.rls_forced||!1,replica_identity:e.replica_identity||"default"},recommendations:[]},t=[];return e.rls_enabled||t.push({type:"security",priority:"high",message:"Considere habilitar Row Level Security (RLS) para esta tabela",action:"enable_rls"}),e.dead_row_count&&e.row_count&&e.dead_row_count/e.row_count>.1&&t.push({type:"performance",priority:"medium",message:"Tabela tem muitas linhas mortas, considere executar VACUUM",action:"vacuum_table"}),e.seq_scan_count&&e.idx_scan_count&&e.seq_scan_count>e.idx_scan_count&&t.push({type:"performance",priority:"medium",message:"Tabela tem mais sequential scans que index scans, considere adicionar \xedndices",action:"add_indexes"}),a.recommendations=t,_.R.success({action:"table_analyzed",tableName:o,analysis:a,timestamp:new Date().toISOString()})}case"refresh_stats":{let e=await c.getDatabaseSummary();return _.R.success({action:"stats_refreshed",summary:{totalTables:e.totalTables,totalSize:e.totalSize,largestTables:e.largestTables.slice(0,5).map(e=>({name:e.name,size:e.size,bytes:e.bytes}))},timestamp:new Date().toISOString()})}default:return _.R.error(`A\xe7\xe3o '${s}' n\xe3o suportada`,"UNSUPPORTED_ACTION",400)}}catch(e){if(c.kg.error("Erro no POST tabelas Supabase",{error:e}),e instanceof Error)return _.R.error(`Erro na opera\xe7\xe3o: ${e.message}`,"SUPABASE_OPERATION_ERROR",500);return _.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let S=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/supabase/tables/route",pathname:"/api/supabase/tables",filename:"route",bundlePath:"app/api/supabase/tables/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\tables\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:R,staticGenerationAsyncStorage:x,serverHooks:f}=S,g="/api/supabase/tables/route";function E(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:x})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[8948,5972,9557,7410,2972,8525],()=>t(19623));module.exports=r})();