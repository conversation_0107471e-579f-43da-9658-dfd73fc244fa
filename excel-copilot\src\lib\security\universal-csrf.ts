/**
 * Universal CSRF Protection - ÁREA 7 SEGURANÇA (18/06/2025)
 * Middleware universal para aplicar proteção CSRF em todos os endpoints sensíveis
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '../logger';
import { generateCSRFToken, validateCSRFToken } from './edge-csrf';

// Endpoints que SEMPRE requerem proteção CSRF
const CSRF_REQUIRED_PATTERNS = [
  // APIs de autenticação
  /^\/api\/auth\/(?!signin|callback|session)/i,
  
  // APIs de dados sensíveis
  /^\/api\/workbooks/i,
  /^\/api\/workbook/i,
  /^\/api\/chat/i,
  /^\/api\/ai/i,
  
  // APIs de pagamento
  /^\/api\/payment/i,
  /^\/api\/subscription/i,
  /^\/api\/stripe/i,
  
  // APIs administrativas
  /^\/api\/admin/i,
  /^\/api\/user/i,
  
  // APIs de configuração
  /^\/api\/settings/i,
  /^\/api\/config/i,
  
  // Webhooks (exceto alguns específicos)
  /^\/api\/webhooks\/(?!stripe|github)/i,
];

// Endpoints que são EXCLUÍDOS da proteção CSRF
const CSRF_EXCLUDED_PATTERNS = [
  // Webhooks externos (têm sua própria validação)
  /^\/api\/webhooks\/stripe/i,
  /^\/api\/webhooks\/github/i,
  /^\/api\/webhooks\/vercel/i,
  
  // APIs públicas de leitura
  /^\/api\/health/i,
  /^\/api\/status/i,
  /^\/api\/metrics/i,
  /^\/api\/api-docs/i,
  
  // NextAuth callbacks
  /^\/api\/auth\/signin/i,
  /^\/api\/auth\/callback/i,
  /^\/api\/auth\/session/i,
];

/**
 * Verifica se um endpoint requer proteção CSRF
 */
export function requiresCSRFProtection(pathname: string, method: string): boolean {
  // Métodos seguros não requerem CSRF
  if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
    return false;
  }

  // Verificar exclusões primeiro
  if (CSRF_EXCLUDED_PATTERNS.some(pattern => pattern.test(pathname))) {
    return false;
  }

  // Verificar se está na lista de endpoints obrigatórios
  return CSRF_REQUIRED_PATTERNS.some(pattern => pattern.test(pathname));
}

/**
 * Middleware universal de CSRF
 */
export function withUniversalCSRF(
  handler: (req: NextRequest) => Promise<NextResponse> | NextResponse
) {
  return async function (req: NextRequest): Promise<NextResponse> {
    const pathname = req.nextUrl.pathname;
    const method = req.method;

    // Verificar se requer proteção CSRF
    if (!requiresCSRFProtection(pathname, method)) {
      return handler(req);
    }

    try {
      // Obter token do header ou cookie
      const headerToken = req.headers.get('x-csrf-token');
      const cookieHeader = req.headers.get('cookie');

      let cookieToken: string | null = null;
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').reduce(
          (acc, cookie) => {
            const [key, value] = cookie.trim().split('=');
            if (key && value) {
              acc[key] = value;
            }
            return acc;
          },
          {} as Record<string, string>
        );
        cookieToken = cookies['csrf_token'] || null;
      }

      const token = headerToken || cookieToken;
      
      if (!token) {
        logger.warn(`CSRF: Token ausente para ${method} ${pathname}`, {
          userAgent: req.headers.get('user-agent'),
          ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
        });

        return NextResponse.json(
          {
            error: 'Token CSRF ausente',
            code: 'CSRF_TOKEN_MISSING',
            message: 'Esta operação requer um token CSRF válido'
          },
          { status: 403 }
        );
      }

      // Validar token CSRF
      const secret = process.env.SECURITY_CSRF_SECRET || 'default-csrf-secret-change-in-production';
      const sessionData = Date.now().toString(); // Simplificado para Edge Runtime
      
      const isValid = await validateCSRFToken(token, secret, sessionData);
      
      if (!isValid) {
        logger.warn(`CSRF: Token inválido para ${method} ${pathname}`, {
          tokenLength: token.length,
          userAgent: req.headers.get('user-agent'),
          ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
        });

        return NextResponse.json(
          {
            error: 'Token CSRF inválido',
            code: 'CSRF_TOKEN_INVALID',
            message: 'O token CSRF fornecido é inválido ou expirou'
          },
          { status: 403 }
        );
      }

      // Token válido, prosseguir com o handler
      logger.debug(`CSRF: Token válido para ${method} ${pathname}`);
      return handler(req);

    } catch (error) {
      logger.error(`CSRF: Erro durante validação para ${method} ${pathname}`, error);

      return NextResponse.json(
        {
          error: 'Erro interno durante validação CSRF',
          code: 'CSRF_VALIDATION_ERROR'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Gera um novo token CSRF para o cliente
 */
export async function generateCSRFTokenForClient(): Promise<{
  token: string;
  cookie: string;
}> {
  const secret = process.env.SECURITY_CSRF_SECRET || 'default-csrf-secret-change-in-production';
  const sessionData = Date.now().toString();
  
  const token = await generateCSRFToken(secret, sessionData);
  
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
    maxAge: 60 * 60 * 1000, // 1 hora
  };

  const cookie = `csrf_token=${token}; HttpOnly; ${cookieOptions.secure ? 'Secure; ' : ''}SameSite=${cookieOptions.sameSite}; Path=${cookieOptions.path}; Max-Age=${cookieOptions.maxAge}`;

  return { token, cookie };
}

/**
 * API endpoint para obter token CSRF
 */
export async function handleCSRFTokenRequest(req: NextRequest): Promise<NextResponse> {
  if (req.method !== 'GET') {
    return NextResponse.json(
      { error: 'Método não permitido' },
      { status: 405 }
    );
  }

  try {
    const { token, cookie } = await generateCSRFTokenForClient();

    const response = NextResponse.json({
      token,
      message: 'Token CSRF gerado com sucesso'
    });

    response.headers.set('Set-Cookie', cookie);
    
    return response;

  } catch (error) {
    logger.error('Erro ao gerar token CSRF', error);
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Exportar configurações para uso externo
export const CSRF_CONFIG = {
  HEADER_NAME: 'x-csrf-token',
  COOKIE_NAME: 'csrf_token',
  REQUIRED_PATTERNS: CSRF_REQUIRED_PATTERNS,
  EXCLUDED_PATTERNS: CSRF_EXCLUDED_PATTERNS,
};
