"use strict";(()=>{var e={};e.id=6717,e.ids=[6717],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},66384:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>C,patchFetch:()=>F,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var i={};r.r(i),r.d(i,{default:()=>u});var a={};r.r(a),r.d(a,{GET:()=>h});var o=r(49303),n=r(88716),l=r(60670),s=r(55661);let p=process.env.NEXT_PUBLIC_APP_URL||"https://excel-copilot.vercel.app";function u(){let e=new Date().toISOString();return[{url:`${p}`,lastModified:e,changeFrequency:"weekly",priority:1},{url:`${p}/dashboard`,lastModified:e,changeFrequency:"weekly",priority:.9},{url:`${p}/pricing`,lastModified:e,changeFrequency:"monthly",priority:.8},{url:`${p}/features`,lastModified:e,changeFrequency:"monthly",priority:.8},{url:`${p}/login`,lastModified:e,changeFrequency:"yearly",priority:.5},{url:`${p}/sign-up`,lastModified:e,changeFrequency:"yearly",priority:.5},{url:`${p}/download`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${p}/terms`,lastModified:e,changeFrequency:"yearly",priority:.4},{url:`${p}/privacy`,lastModified:e,changeFrequency:"yearly",priority:.4},{url:`${p}/documentation`,lastModified:e,changeFrequency:"monthly",priority:.7},{url:`${p}/support`,lastModified:e,changeFrequency:"monthly",priority:.6}]}var d=r(60707);let c={...i},y=c.default,m=c.generateSitemaps;if("function"!=typeof y)throw Error('Default export is missing in "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\sitemap.ts"');async function h(e,t){let r;let{__metadata_id__:i,...a}=t.params||{},o=m?await m():null;if(o&&null==(r=o.find(e=>{let t=e.id.toString();return(t+=".xml")===i})?.id))return new s.NextResponse("Not Found",{status:404});let n=await y({id:r}),l=(0,d.resolveRouteData)(n,"sitemap");return new s.NextResponse(l,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let f=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=C%3A%5CUsers%5CMn%5CDesktop%5Cdo%20vscode%5Cexcel-copilot%5Csrc%5Capp%5Csitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:v}=f,C="/sitemap.xml/route";function F(){return(0,l.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1346],()=>r(66384));module.exports=i})();