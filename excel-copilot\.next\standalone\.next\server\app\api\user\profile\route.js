"use strict";(()=>{var e={};e.id=3744,e.ids=[3744],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},48281:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>z,requestAsyncStorage:()=>v,routeModule:()=>h,serverHooks:()=>j,staticGenerationAsyncStorage:()=>b});var o={};t.r(o),t.d(o,{GET:()=>f,PATCH:()=>q,dynamic:()=>m,runtime:()=>g});var i=t(49303),s=t(88716),a=t(60670),n=t(87070),u=t(45609),l=t(7410),p=t(43895),d=t(63841),c=t(81628);let m="force-dynamic",g="nodejs",x=l.z.object({name:l.z.string().min(1,"Nome \xe9 obrigat\xf3rio").max(100,"Nome muito longo").optional(),email:l.z.string().email("Email inv\xe1lido").optional(),image:l.z.string().url("URL de imagem inv\xe1lida").optional(),preferences:l.z.object({theme:l.z.enum(["light","dark","system"]).optional(),language:l.z.enum(["pt-BR","en-US"]).optional(),notifications:l.z.object({email:l.z.boolean().optional(),push:l.z.boolean().optional(),marketing:l.z.boolean().optional()}).optional(),privacy:l.z.object({profileVisible:l.z.boolean().optional(),shareUsageData:l.z.boolean().optional()}).optional()}).optional()});async function f(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user)return n.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let r=e.user.id,t=await d.prisma.user.findUnique({where:{id:r},select:{id:!0,name:!0,email:!0,image:!0,emailVerified:!0,createdAt:!0,updatedAt:!0,lastLoginAt:!0,loginCount:!0}});if(!t)return n.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado."},{status:404});let[o,i]=await Promise.all([d.prisma.workbook.count({where:{userId:r}}),d.prisma.subscription.findFirst({where:{userId:r,OR:[{status:"active"},{status:"trialing"}]},orderBy:{createdAt:"desc"}})]),s={...t,preferences:{theme:"system",language:"pt-BR",notifications:{email:!0,push:!0,marketing:!1},privacy:{profileVisible:!0,shareUsageData:!1}},stats:{workbooksCount:o,memberSince:t.createdAt,lastLogin:t.lastLoginAt,loginCount:t.loginCount},subscription:i?{plan:i.plan,status:i.status,currentPeriodEnd:i.currentPeriodEnd}:null};return p.kg.info("Perfil do usu\xe1rio consultado",{userId:r}),n.NextResponse.json({profile:s})}catch(e){return p.kg.error("Erro ao buscar perfil do usu\xe1rio",{error:e}),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}async function q(e){try{let r=await (0,u.getServerSession)(c.L);if(!r?.user)return n.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let t=r.user.id,o=await e.json(),i=x.safeParse(o);if(!i.success)return n.NextResponse.json({error:"Dados inv\xe1lidos",details:i.error.errors},{status:400});let s=i.data,a={};if(void 0!==s.name&&(a.name=s.name),void 0!==s.email){if(await d.prisma.user.findFirst({where:{email:s.email,NOT:{id:t}}}))return n.NextResponse.json({error:"Este email j\xe1 est\xe1 em uso por outro usu\xe1rio."},{status:409});a.email=s.email,a.emailVerified=null}void 0!==s.image&&(a.image=s.image);let l=await d.prisma.user.update({where:{id:t},data:{...a,updatedAt:new Date},select:{id:!0,name:!0,email:!0,image:!0,emailVerified:!0,updatedAt:!0}});return p.kg.info("Perfil do usu\xe1rio atualizado",{userId:t,updatedFields:Object.keys(a)}),n.NextResponse.json({message:"Perfil atualizado com sucesso",profile:l})}catch(e){return p.kg.error("Erro ao atualizar perfil do usu\xe1rio",{error:e}),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:v,staticGenerationAsyncStorage:b,serverHooks:j}=h,y="/api/user/profile/route";function z(){return(0,a.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:b})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(48281));module.exports=o})();