(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},63021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(44880),r(65675),r(12523);var a=r(23191),s=r(88716),n=r(37922),o=r.n(n),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44880)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx"],u="/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81710:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79404,23)),Promise.resolve().then(r.bind(r,55685)),Promise.resolve().then(r.bind(r,33294)),Promise.resolve().then(r.bind(r,91664))},37202:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},941:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},75290:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55685:(e,t,r)=>{"use strict";r.d(t,{CommandExamplesWrapper:()=>w});var a=r(10326),s=r(35047),n=r(72257),o=r(95396),i=r(76557);let l=(0,i.Z)("FilterX",[["path",{d:"M13.013 3H2l8 9.46V19l4 2v-8.54l.9-1.055",key:"1fi1da"}],["path",{d:"m22 3-5 5",key:"12jva0"}],["path",{d:"m17 3 5 5",key:"k36vhe"}]]);var d=r(4198);let c=(0,i.Z)("Sigma",[["path",{d:"M18 7V4H6l6 8-6 8h12v-3",key:"zis8ev"}]]);var u=r(1572),m=r(35351),p=r(941),x=r(17577),f=r(38443),h=r(91664),g=r(2578),b=r(41190),y=r(50258),v=r(3236),N=r(82015),j=(r(75290),r(72607),r(60850),r(14831),r(82631),r(51223),r(56627));j.s6,f.C,h.Button,g.ZP,b.Z,y.MD,y.Kk,v.x,N.Z,j.V6;var A=r(29752);function k({onSelect:e}){let[t,r]=(0,x.useState)(!1),[s,i]=(0,x.useState)(-1),[f,g]=(0,x.useState)(!1),b=[{name:"An\xe1lise Matem\xe1tica",icon:a.jsx(n.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B","Calcule a m\xe9dia da coluna Vendas","Qual \xe9 o maior valor na coluna de Receita?","Encontre o desvio padr\xe3o dos dados na coluna E","Multiplique os valores da coluna A por 2","Calcule o percentual de crescimento m\xeas a m\xeas","Fa\xe7a uma an\xe1lise estat\xedstica completa da coluna Valores"]},{name:"Visualiza\xe7\xe3o",icon:a.jsx(o.Z,{className:"h-5 w-5"}),examples:["Crie um gr\xe1fico de barras com os dados das colunas A e B","Fa\xe7a um gr\xe1fico de linha de vendas por m\xeas","Gere um gr\xe1fico de pizza com os valores da tabela","Mostre um gr\xe1fico de dispers\xe3o entre pre\xe7o e quantidade","Crie um histograma da coluna Idades","Fa\xe7a um dashboard com 3 gr\xe1ficos: barras, linha e pizza","Crie um mapa de calor com os dados de vendas por regi\xe3o"]},{name:"Filtros e Organiza\xe7\xe3o",icon:a.jsx(l,{className:"h-5 w-5"}),examples:["Filtre os dados onde Vendas > 1000","Ordene a tabela por valor, do maior para o menor","Mostre apenas registros onde a coluna Regi\xe3o \xe9 'Sul'","Filtre dados do m\xeas de Janeiro","Remova as linhas com valores nulos","Filtre os 10 maiores clientes por volume de compras","Agrupe por categoria e mostre apenas os grupos com mais de 5 itens"]},{name:"Formata\xe7\xe3o",icon:a.jsx(d.Z,{className:"h-5 w-5"}),examples:["Converta a planilha atual para formato de tabela","Aplique formata\xe7\xe3o condicional na coluna de valores","Destaque c\xe9lulas com valores negativos em vermelho","Formate a coluna de datas no padr\xe3o DD/MM/AAAA","Adicione uma linha de totais ao final da tabela","Formate os cabe\xe7alhos com fundo azul e texto branco em negrito","Adicione bordas em todas as c\xe9lulas da tabela"]},{name:"Tabelas Din\xe2micas",icon:a.jsx(c,{className:"h-5 w-5"}),examples:["Crie uma tabela din\xe2mica agrupando vendas por regi\xe3o","Fa\xe7a uma tabela din\xe2mica com vendas por produto e vendedor","Gere uma tabela din\xe2mica de receitas mensais por categoria","Crie um resumo de vendas por trimestre e regi\xe3o","Fa\xe7a uma tabela din\xe2mica com subtotais por departamento"]},{name:"An\xe1lise Avan\xe7ada",icon:a.jsx(u.Z,{className:"h-5 w-5"}),examples:["Fa\xe7a uma an\xe1lise de correla\xe7\xe3o entre as colunas pre\xe7o e demanda","Gere estat\xedsticas descritivas de todas as colunas num\xe9ricas","Crie segmenta\xe7\xe3o de dados por faixa et\xe1ria: 0-18, 19-35, 36-60, 60+","Aplique regress\xe3o linear e preveja valores futuros","Fa\xe7a uma an\xe1lise de sazonalidade nos dados mensais","Identifique outliers na coluna de vendas e sugira tratamentos","Calcule a previs\xe3o de vendas para os pr\xf3ximos 3 meses usando s\xe9rie temporal","Agrupe clientes por comportamento de compra usando K-means","Fa\xe7a uma an\xe1lise de cesta de compras para identificar produtos complementares","Crie um modelo de pontua\xe7\xe3o para classificar leads por potencial"]},{name:"Perguntas em Linguagem Natural",icon:a.jsx(m.Z,{className:"h-5 w-5"}),examples:["Quais foram os 3 melhores vendedores no \xfaltimo trimestre?","Qual regi\xe3o teve a maior queda nas vendas?","Como est\xe1 o desempenho das vendas comparado com o mesmo per\xedodo do ano passado?","Quais produtos tiveram crescimento acima de 10% nos \xfaltimos 6 meses?","Qual \xe9 a tend\xeancia de vendas para o produto X?"]}],N=b[0]||{name:"An\xe1lise Matem\xe1tica",icon:a.jsx(n.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B"]},j=N.examples[0]||"",k=(0,x.useCallback)((t,r,a)=>{let s=t.key;if("Enter"===s||" "===s){t.preventDefault();let s=b[r];s&&s.examples[a]&&e(s.examples[a])}if("ArrowDown"===s||"ArrowUp"===s){t.preventDefault();let e=b[r];if(!e)return;let n=e.examples.length-1;"ArrowDown"===s&&a<n?(i(a+1),document.getElementById(`example-${r}-${a+1}`)?.focus()):"ArrowUp"===s&&a>0&&(i(a-1),document.getElementById(`example-${r}-${a-1}`)?.focus())}},[e]);return a.jsx("div",{className:"space-y-4 w-full",children:(0,a.jsxs)(A.Zb,{className:"border shadow-sm bg-card",children:[a.jsx(A.Ol,{className:"pb-2",children:(0,a.jsxs)(A.ll,{className:"text-lg flex items-center",children:[a.jsx(u.Z,{className:"h-6 w-6 mr-2 text-primary","aria-hidden":"true"}),a.jsx("span",{className:"text-enhanced-contrast",children:"Exemplos de Comandos"})]})}),(0,a.jsxs)(A.aY,{children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("div",{className:"p-1 rounded-md bg-primary/10 text-primary","aria-hidden":"true",children:N.icon}),a.jsx("span",{className:"text-xs font-medium text-muted-foreground",children:N.name})]}),a.jsx(h.Button,{variant:"outline",className:"w-full justify-start text-left font-normal h-auto py-3 border-primary/20 bg-primary/5 hover:bg-primary/10 a11y-focus",onClick:()=>e(j),"aria-label":`Usar comando: ${j}`,children:a.jsx("span",{className:"line-clamp-2 text-xs sm:text-sm",children:j})})]}),t?(0,a.jsxs)(a.Fragment,{children:[a.jsx(v.x,{className:"h-[200px] sm:h-[250px] md:h-[300px] pr-4",role:"list","aria-label":"Lista de comandos por categoria",children:a.jsx("div",{className:"space-y-3 sm:space-y-4",children:b.map((t,r)=>(0,a.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",role:"group","aria-labelledby":`category-${r}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"p-1 sm:p-1.5 rounded-md bg-muted text-muted-foreground","aria-hidden":"true",children:t.icon}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",id:`category-${r}`,children:t.name})]}),a.jsx("div",{className:"grid grid-cols-1 gap-1 sm:gap-1.5 pl-5 sm:pl-7",role:"list",children:t.examples.slice(0,f?4:void 0).map((t,s)=>a.jsx(y.Kk,{id:`example-${r}-${s}`,variant:"ghost",size:"sm",className:"justify-start h-auto py-1 sm:py-1.5 px-1.5 sm:px-2 text-xs sm:text-sm font-normal text-muted-foreground hover:text-foreground mobile-enhanced-tap a11y-focus",actionId:`${r}-${s}`,onAction:()=>e(t),onKeyDown:e=>k(e,r,s),"aria-label":`Usar comando: ${t}`,children:a.jsx("span",{className:"line-clamp-1",children:t})},s))})]},r))})}),(0,a.jsxs)(h.Button,{onClick:()=>r(!1),variant:"ghost",size:"sm",className:"w-full mt-2 text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"true","aria-label":"Mostrar menos exemplos",children:[a.jsx("span",{children:"Mostrar menos"}),a.jsx(p.Z,{className:"h-4 w-4 ml-1 transform rotate-180"})]})]}):(0,a.jsxs)(h.Button,{onClick:()=>r(!0),variant:"ghost",size:"sm",className:"w-full text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"false","aria-label":"Mostrar mais exemplos",children:[a.jsx("span",{children:"Explorar mais sugest\xf5es"}),a.jsx(p.Z,{className:"h-4 w-4 ml-1"})]})]})]})})}function w(){let e=(0,s.useRouter)();return a.jsx(k,{onSelect:t=>{e.push(`/dashboard?command=${encodeURIComponent(t)}`)}})}},33294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{HeroSection:()=>ev});var a=r(10326),s=r(17577),n=r.n(s),o=r(21058),i=r(34374),l=r(31722),d=r(1572),c=r(24230),u=r(32933),m=r(90434);function p(e,t,r,a){return new(r||(r=Promise))(function(s,n){function o(e){try{l(a.next(e))}catch(e){n(e)}}function i(e){try{l(a.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,i)}l((a=a.apply(e,t||[])).next())})}function x(e,t){var r,a,s,n,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return n={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function i(n){return function(i){return function(n){if(r)throw TypeError("Generator is already executing.");for(;o;)try{if(r=1,a&&(s=2&n[0]?a.return:n[0]?a.throw||((s=a.return)&&s.call(a),0):a.next)&&!(s=s.call(a,n[1])).done)return s;switch(a=0,s&&(n=[2&n[0],s.value]),n[0]){case 0:case 1:s=n;break;case 4:return o.label++,{value:n[1],done:!1};case 5:o.label++,a=n[1],n=[0];continue;case 7:n=o.ops.pop(),o.trys.pop();continue;default:if(!(s=(s=o.trys).length>0&&s[s.length-1])&&(6===n[0]||2===n[0])){o=0;continue}if(3===n[0]&&(!s||n[1]>s[0]&&n[1]<s[3])){o.label=n[1];break}if(6===n[0]&&o.label<s[1]){o.label=s[1],s=n;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(n);break}s[2]&&o.ops.pop(),o.trys.pop();continue}n=t.call(e,o)}catch(e){n=[6,e],a=0}finally{r=s=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,i])}}}function f(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],a=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&a>=e.length&&(e=void 0),{value:e&&e[a++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function h(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var a,s,n=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)o.push(a.value)}catch(e){s={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return o}function g(e,t,r){if(r||2==arguments.length)for(var a,s=0,n=t.length;s<n;s++)!a&&s in t||(a||(a=Array.prototype.slice.call(t,0,s)),a[s]=t[s]);return e.concat(a||Array.prototype.slice.call(t))}function b(e,t,r,a,s){for(var n=[],o=5;o<arguments.length;o++)n[o-5]=arguments[o];return p(this,void 0,void 0,function(){var o,i,l,d,c;return x(this,function(u){switch(u.label){case 0:u.trys.push([0,12,13,14]),i=(o=f(n)).next(),u.label=1;case 1:if(i.done)return[3,11];switch(typeof(l=i.value)){case"string":return[3,2];case"number":return[3,4];case"function":return[3,6]}return[3,8];case 2:return[4,function(e,t,r,a,s,n){return p(this,void 0,void 0,function(){var o,i;return x(this,function(l){switch(l.label){case 0:var d,c;return d=o=e.textContent||"",c=h(r).slice(0),i=g(g([],h(d),!1),[NaN],!1).findIndex(function(e,t){return c[t]!==e}),[4,function(e,t,r,a,s){return p(this,void 0,void 0,function(){var n,o,i,l,d,c,u,m,p,g,b,v;return x(this,function(N){switch(N.label){case 0:if(n=t,s){for(o=0,i=1;i<t.length;i++)if(d=(l=h([t[i-1],t[i]],2))[0],(c=l[1]).length>d.length||""===c){o=i;break}n=t.slice(o,t.length)}N.label=1;case 1:N.trys.push([1,6,7,8]),m=(u=f(function(e){var t,r,a,s,n,o;return x(this,function(i){switch(i.label){case 0:t=function(e){return x(this,function(t){switch(t.label){case 0:return[4,{op:function(t){return requestAnimationFrame(function(){return t.textContent=e})},opCode:function(t){var r=t.textContent||"";return""===e||r.length>e.length?"DELETE":"WRITING"}}];case 1:return t.sent(),[2]}})},i.label=1;case 1:i.trys.push([1,6,7,8]),a=(r=f(e)).next(),i.label=2;case 2:return a.done?[3,5]:(s=a.value,[5,t(s)]);case 3:i.sent(),i.label=4;case 4:return a=r.next(),[3,2];case 5:return[3,8];case 6:return n={error:i.sent()},[3,8];case 7:try{a&&!a.done&&(o=r.return)&&o.call(r)}finally{if(n)throw n.error}return[7];case 8:return[2]}})}(n))).next(),N.label=2;case 2:return m.done?[3,5]:(g="WRITING"===(p=m.value).opCode(e)?r+r*(Math.random()-.5):a+a*(Math.random()-.5),p.op(e),[4,y(g)]);case 3:N.sent(),N.label=4;case 4:return m=u.next(),[3,2];case 5:return[3,8];case 6:return b={error:N.sent()},[3,8];case 7:try{m&&!m.done&&(v=u.return)&&v.call(u)}finally{if(b)throw b.error}return[7];case 8:return[2]}})})}(e,g(g([],h(function(e,t,r){var a,s;return void 0===r&&(r=0),x(this,function(n){switch(n.label){case 0:s=(a=t(e)).length,n.label=1;case 1:return s>r?[4,a.slice(0,--s).join("")]:[3,3];case 2:return n.sent(),[3,1];case 3:return[2]}})}(o,t,i)),!1),h(function(e,t,r){var a,s;return void 0===r&&(r=0),x(this,function(n){switch(n.label){case 0:s=(a=t(e)).length,n.label=1;case 1:return r<s?[4,a.slice(0,++r).join("")]:[3,3];case 2:return n.sent(),[3,1];case 3:return[2]}})}(r,t,i)),!1),a,s,n)];case 1:return l.sent(),[2]}})})}(e,t,l,r,a,s)];case 3:case 5:case 7:return u.sent(),[3,10];case 4:return[4,y(l)];case 6:return[4,l.apply(void 0,g([e,t,r,a,s],h(n),!1))];case 8:return[4,l];case 9:u.sent(),u.label=10;case 10:return i=o.next(),[3,1];case 11:return[3,14];case 12:return d={error:u.sent()},[3,14];case 13:try{i&&!i.done&&(c=o.return)&&c.call(o)}finally{if(d)throw d.error}return[7];case 14:return[2]}})})}function y(e){return p(this,void 0,void 0,function(){return x(this,function(t){switch(t.label){case 0:return[4,new Promise(function(t){return setTimeout(t,e)})];case 1:return t.sent(),[2]}})})}!function(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&"undefined"!=typeof document){var a=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css","top"===r&&a.firstChild?a.insertBefore(s,a.firstChild):a.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e))}}(".index-module_type__E-SaG::after {\n  content: '|';\n  animation: index-module_cursor__PQg0P 1.1s infinite step-start;\n}\n\n@keyframes index-module_cursor__PQg0P {\n  50% {\n    opacity: 0;\n  }\n}\n");var v=(0,s.memo)((0,s.forwardRef)(function(e,t){var r=e.sequence,a=e.repeat,o=e.className,i=e.speed,l=void 0===i?40:i,d=e.deletionSpeed,c=e.omitDeletionAnimation,u=void 0!==c&&c,m=e.preRenderFirstString,p=e.wrapper,x=e.splitter,f=void 0===x?function(e){return g([],h(e),!1)}:x,y=e.cursor,v=void 0===y||y,N=e.style,j=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(a=Object.getOwnPropertySymbols(e);s<a.length;s++)0>t.indexOf(a[s])&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(r[a[s]]=e[a[s]])}return r}(e,["sequence","repeat","className","speed","deletionSpeed","omitDeletionAnimation","preRenderFirstString","wrapper","splitter","cursor","style"]),A=j["aria-label"],k=j["aria-hidden"],w=j.role;d||(d=l);var E=[,,].fill(40);[l,d].forEach(function(e,t){switch(typeof e){case"number":E[t]=Math.abs(e-100);break;case"object":var r=e.type,a=e.value;if("number"!=typeof a)break;"keyStrokeDelayInMs"===r&&(E[t]=a)}});var R,T,C,O,S,P,D,_,M=E[0],I=E[1],L=(void 0===R&&(R=null),T=(0,s.useRef)(R),(0,s.useEffect)(function(){t&&("function"==typeof t?t(T.current):t.current=T.current)},[t]),T),F="index-module_type__E-SaG";C=o?"".concat(v?F+" ":"").concat(o):v?F:"",O=(0,s.useRef)(function(){var e,t=r;a===1/0?e=b:"number"==typeof a&&(t=Array(1+a).fill(r).flat());var s=e?g(g([],h(t),!1),[e],!1):g([],h(t),!1);return b.apply(void 0,g([L.current,f,M,I,u],h(s),!1)),function(){L.current}}),S=(0,s.useRef)(),P=(0,s.useRef)(!1),D=(0,s.useRef)(!1),_=h((0,s.useState)(0),2)[1],P.current&&(D.current=!0),(0,s.useEffect)(function(){return P.current||(S.current=O.current(),P.current=!0),_(function(e){return e+1}),function(){D.current&&S.current&&S.current()}},[]);var z=void 0!==m&&m?r.find(function(e){return"string"==typeof e})||"":null;return n().createElement(void 0===p?"span":p,{"aria-hidden":k,"aria-label":A,role:w,style:N,className:C,children:A?n().createElement("span",{"aria-hidden":"true",ref:L,children:z}):z,ref:A?void 0:L})}),function(e,t){return!0}),N=r(30660),j=r(49844),A=r(18423),k=r(62637),w=r(9997),E=r(44675),R=r(69274),T=r(8564),C=r(96601),O=r(23958),S=r(85586),P=r.n(S),D=r(20119),_=r.n(D),M=r(81711),I=r.n(M),L=r(41135),F=r(96195),z=r(61595),$=r(49432),Z=r(45588),q=r(22582),B=r(2645),V=r(2656),U=r(81656),G=r(81546),W=["type","layout","connectNulls","ref"],H=["key"];function K(e){return(K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Q(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}function Y(){return(Y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(t){eo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ee(e){return function(e){if(Array.isArray(e))return et(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return et(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return et(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function et(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function er(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,ei(a.key),a)}}function ea(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ea=function(){return!!e})()}function es(e){return(es=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function en(e,t){return(en=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eo(e,t,r){return(t=ei(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ei(e){var t=function(e,t){if("object"!=K(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=K(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==K(t)?t:t+""}var el=function(e){var t,r;function a(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,a);for(var e,t,r,s=arguments.length,n=Array(s),o=0;o<s;o++)n[o]=arguments[o];return t=a,r=[].concat(n),t=es(t),eo(e=function(e,t){if(t&&("object"===K(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ea()?Reflect.construct(t,r||[],es(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),eo(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),eo(e,"getStrokeDasharray",function(t,r,s){var n=s.reduce(function(e,t){return e+t});if(!n)return e.generateSimpleStrokeDasharray(r,t);for(var o=t%n,i=r-t,l=[],d=0,c=0;d<s.length;c+=s[d],++d)if(c+s[d]>o){l=[].concat(ee(s.slice(0,d)),[o-c]);break}var u=l.length%2==0?[0,i]:[i];return[].concat(ee(a.repeat(s,Math.floor(t/n))),ee(l),u).map(function(e){return"".concat(e,"px")}).join(", ")}),eo(e,"id",(0,B.EL)("recharts-line-")),eo(e,"pathRef",function(t){e.mainCurve=t}),eo(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),eo(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&en(e,t)}(a,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,a=r.points,s=r.xAxis,o=r.yAxis,i=r.layout,l=r.children,d=(0,V.NN)(l,q.W);if(!d)return null;var c=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,G.F$)(e.payload,t)}};return n().createElement($.m,{clipPath:e?"url(#clipPath-".concat(t,")"):null},d.map(function(e){return n().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:a,xAxis:s,yAxis:o,layout:i,dataPointFormatter:c})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var s=this.props,o=s.dot,i=s.points,l=s.dataKey,d=(0,V.L6)(this.props,!1),c=(0,V.L6)(o,!0),u=i.map(function(e,t){var r=J(J(J({key:"dot-".concat(t),r:3},d),c),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:l,payload:e.payload,points:i});return a.renderDotItem(o,r)}),m={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return n().createElement($.m,Y({className:"recharts-line-dots",key:"dots"},m),u)}},{key:"renderCurveStatically",value:function(e,t,r,a){var s=this.props,o=s.type,i=s.layout,l=s.connectNulls,d=(s.ref,Q(s,W)),c=J(J(J({},(0,V.L6)(d,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},a),{},{type:o,layout:i,connectNulls:l});return n().createElement(F.H,Y({},c,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,a=this.props,s=a.points,o=a.strokeDasharray,i=a.isAnimationActive,l=a.animationBegin,d=a.animationDuration,c=a.animationEasing,u=a.animationId,m=a.animateNewValues,p=a.width,x=a.height,f=this.state,h=f.prevPoints,g=f.totalLength;return n().createElement(O.ZP,{begin:l,duration:d,isActive:i,easing:c,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(a){var n,i=a.t;if(h){var l=h.length/s.length,d=s.map(function(e,t){var r=Math.floor(t*l);if(h[r]){var a=h[r],s=(0,B.k4)(a.x,e.x),n=(0,B.k4)(a.y,e.y);return J(J({},e),{},{x:s(i),y:n(i)})}if(m){var o=(0,B.k4)(2*p,e.x),d=(0,B.k4)(x/2,e.y);return J(J({},e),{},{x:o(i),y:d(i)})}return J(J({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(d,e,t)}var c=(0,B.k4)(0,g)(i);if(o){var u="".concat(o).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});n=r.getStrokeDasharray(c,g,u)}else n=r.generateSimpleStrokeDasharray(g,c);return r.renderCurveStatically(s,e,t,{strokeDasharray:n})})}},{key:"renderCurve",value:function(e,t){var r=this.props,a=r.points,s=r.isAnimationActive,n=this.state,o=n.prevPoints,i=n.totalLength;return s&&a&&a.length&&(!o&&i>0||!I()(o,a))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(a,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,a=t.dot,s=t.points,o=t.className,i=t.xAxis,l=t.yAxis,d=t.top,c=t.left,u=t.width,m=t.height,p=t.isAnimationActive,x=t.id;if(r||!s||!s.length)return null;var f=this.state.isAnimationFinished,h=1===s.length,g=(0,L.Z)("recharts-line",o),b=i&&i.allowDataOverflow,y=l&&l.allowDataOverflow,v=b||y,N=_()(x)?this.id:x,j=null!==(e=(0,V.L6)(a,!1))&&void 0!==e?e:{r:3,strokeWidth:2},A=j.r,k=j.strokeWidth,w=((0,V.jf)(a)?a:{}).clipDot,E=void 0===w||w,R=2*(void 0===A?3:A)+(void 0===k?2:k);return n().createElement($.m,{className:g},b||y?n().createElement("defs",null,n().createElement("clipPath",{id:"clipPath-".concat(N)},n().createElement("rect",{x:b?c:c-u/2,y:y?d:d-m/2,width:b?u:2*u,height:y?m:2*m})),!E&&n().createElement("clipPath",{id:"clipPath-dots-".concat(N)},n().createElement("rect",{x:c-R/2,y:d-R/2,width:u+R,height:m+R}))):null,!h&&this.renderCurve(v,N),this.renderErrorBar(v,N),(h||a)&&this.renderDots(v,E,N),(!p||f)&&Z.e.renderCallByParent(this.props,s))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(ee(e),[0]):e,a=[],s=0;s<t;++s)a=[].concat(ee(a),ee(r));return a}},{key:"renderDotItem",value:function(e,t){var r;if(n().isValidElement(e))r=n().cloneElement(e,t);else if(P()(e))r=e(t);else{var a=t.key,s=Q(t,H),o=(0,L.Z)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=n().createElement(z.o,Y({key:a},s,{className:o}))}return r}}],t&&er(a.prototype,t),r&&er(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(s.PureComponent);eo(el,"displayName","Line"),eo(el,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!U.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),eo(el,"getComposedData",function(e){var t=e.props,r=e.xAxis,a=e.yAxis,s=e.xAxisTicks,n=e.yAxisTicks,o=e.dataKey,i=e.bandSize,l=e.displayedData,d=e.offset,c=t.layout;return J({points:l.map(function(e,t){var l=(0,G.F$)(e,o);return"horizontal"===c?{x:(0,G.Hv)({axis:r,ticks:s,bandSize:i,entry:e,index:t}),y:_()(l)?null:a.scale(l),value:l,payload:e}:{x:_()(l)?null:r.scale(l),y:(0,G.Hv)({axis:a,ticks:n,bandSize:i,entry:e,index:t}),value:l,payload:e}}),layout:c},d)});var ed=r(11327),ec=(0,C.z)({chartName:"LineChart",GraphicalChild:el,axisComponents:[{axisType:"xAxis",AxisComp:k.K},{axisType:"yAxis",AxisComp:w.B}],formatAxisMap:ed.t9}),eu=r(20751),em=r(78948),ep=r(99630),ex=r(38443),ef=r(91664),eh=r(51223),eg=r(35342);let eb=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#8DD1E1"],ey=[{type:"message",content:'Posso ajudar voc\xea a trabalhar com suas planilhas usando comandos em linguagem natural. Por exemplo, tente "Crie uma tabela de vendas por regi\xe3o" ou "Calcule a m\xe9dia da coluna B".'},{type:eg.ox.TABLE,command:"Crie uma tabela com vendas por regi\xe3o",content:[["Regi\xe3o","Vendas","Meta","% Atingimento"],["Norte","12500","15000","83%"],["Sul","18200","16000","114%"],["Leste","14800","14000","106%"],["Oeste","9300","12000","78%"],["Centro","11700","10000","117%"]]},{type:eg.ox.CHART,command:"Crie um gr\xe1fico de vendas por regi\xe3o",chartType:"bar",data:{labels:["Norte","Sul","Leste","Oeste","Centro"],datasets:[{label:"Vendas",data:[12500,18200,14800,9300,11700],backgroundColor:"rgba(53, 162, 235, 0.5)"},{label:"Meta",data:[15e3,16e3,14e3,12e3,1e4],backgroundColor:"rgba(255, 99, 132, 0.5)"}]}},{type:eg.ox.TABLE,command:"Fa\xe7a uma an\xe1lise das vendas",content:[["M\xe9trica","Valor","Status"],["Total de Vendas","R$ 66.500,00","↑ 12%"],["M\xe9dia por Regi\xe3o","R$ 13.300,00","-"],["Maior Desempenho","Sul (114%)","★★★"],["Menor Desempenho","Oeste (78%)","⚠️"],["Regi\xf5es Acima da Meta","3","↑ 1"]]}];function ev(){let[e,t]=(0,s.useState)(0),[r,n]=(0,s.useState)(!1),[p,x]=(0,s.useState)(!1),[f,h]=(0,s.useState)(!1),[g,b]=(0,s.useState)([]),[y,C]=(0,s.useState)(!1),O=function(){i.O.current||(0,o.A)();let[e]=(0,s.useState)(i.n.current);return e}(),S=(0,s.useMemo)(()=>O||y?{initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},disableMotion:!0}:{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},disableMotion:!1},[O,y]),P=(0,s.useCallback)(t=>{n(!0),0===e?b(ey.slice(0,2)):1===e?b(ey.slice(0,4)):2===e&&b(ey)},[e]),D=(0,s.useCallback)(()=>{if(e<ey.length-1){h(!0);let r=e+1;t(r),setTimeout(()=>{h(!1),P(ey[r]?.command||"")},1500)}else t(0),n(!1),b([])},[e,P]);return(0,s.useCallback)(()=>{if(e>0){h(!0);let r=e-1;t(r),setTimeout(()=>{h(!1),P(ey[r]?.command||"")},500)}},[e,P]),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-8 py-8",children:[(0,a.jsxs)("div",{className:"text-left md:w-1/2",children:[(0,a.jsxs)("div",{className:"inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-sm px-3 py-1 mb-4 shadow-sm",children:[a.jsx(d.Z,{className:"h-3 w-3 mr-1 text-blue-600 dark:text-blue-400"}),a.jsx("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"IA para Excel"})]}),a.jsx("h1",{className:"text-4xl md:text-5xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 leading-tight",children:"Planilhas inteligentes com linguagem natural"}),a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl",children:"Excel Copilot transforma comandos em texto simples em planilhas poderosas."}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[a.jsx(ef.Button,{size:"lg",className:"rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 px-6 py-2 text-sm shadow-md font-medium transition-all",asChild:!0,children:(0,a.jsxs)(m.default,{href:"/dashboard",children:["Come\xe7ar agora ",a.jsx(c.Z,{className:"ml-1 h-4 w-4"})]})}),a.jsx(ef.Button,{variant:"outline",size:"lg",className:"rounded-full border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 px-6 py-2 text-sm font-medium transition-all",asChild:!0,children:a.jsx(m.default,{href:"#exemplos",children:"Ver exemplos"})})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),a.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Comandos em linguagem natural para criar planilhas"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),a.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"F\xf3rmulas complexas geradas automaticamente"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[a.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),a.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Visualiza\xe7\xf5es e gr\xe1ficos em segundos"})]})]})]}),a.jsx("div",{className:"md:w-1/2 w-full mt-8 md:mt-0",children:(0,a.jsxs)("div",{className:"rounded-xl border shadow-md bg-white dark:bg-gray-900/60 overflow-hidden backdrop-blur-sm",children:[(0,a.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 border-b flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex space-x-1.5",children:[a.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),a.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),a.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"})]}),a.jsx(ex.C,{variant:"outline",className:"text-xs font-normal hidden xs:inline",children:"Demo Interativa"}),a.jsx(ef.Button,{id:"autoplay-button",variant:"ghost",size:"sm",onClick:()=>x(!p),className:"text-xs","aria-pressed":p,"aria-label":p?"Desativar execu\xe7\xe3o autom\xe1tica":"Ativar execu\xe7\xe3o autom\xe1tica",children:p?"Pausar":"Auto-play"})]}),(0,a.jsxs)("div",{className:"p-2 sm:p-4 min-h-[250px] sm:min-h-[300px] max-h-[350px] sm:max-h-[400px] overflow-y-auto",children:[r&&g.map((e,t)=>a.jsx(l.E.div,{initial:S.initial,animate:S.animate,transition:S.transition,className:"mb-4",children:"message"===e.type?a.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 italic",children:e.content}):e.type===eg.ox.TABLE?a.jsx("div",{className:"overflow-auto max-h-[200px] sm:max-h-[250px]",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-700 text-xs sm:text-sm",role:"table","aria-label":"Dados tabulares de exemplo",children:[a.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:a.jsx("tr",{children:Array.isArray(e.content)&&e.content.length>0?e.content[0]?.map((e,t)=>a.jsx("th",{scope:"col",className:"px-2 py-2 sm:px-3 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e},`header-${t}`)):null})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800",children:Array.isArray(e.content)&&e.content.length>0?e.content.slice(1).map((t,r)=>a.jsx("tr",{className:r%2==0?"bg-white dark:bg-gray-900/80":"bg-gray-50 dark:bg-gray-900/50",children:t.map((t,s)=>a.jsx("td",{className:(0,eh.cn)("px-2 py-2 sm:px-3 sm:py-3 whitespace-nowrap",e.highlightCells?.some(e=>e.row===r+1&&e.col===s)?"bg-blue-100 dark:bg-blue-900/30":""),children:t},s))},r)):null})]})}):e.type===eg.ox.CHART?a.jsx("div",{className:"w-full h-[150px] sm:h-[200px] md:h-[250px] bg-white dark:bg-gray-800 p-2 rounded-md",children:y||O?a.jsx(eN,{data:e.data,chartType:e.chartType}):a.jsx(N.h,{width:"100%",height:"100%",children:"bar"===e.chartType?(0,a.jsxs)(j.v,{data:e.data.datasets[0].data.map((t,r)=>({name:e.data.labels[r],value:t,meta:e.data.datasets[0].label,value2:e.data.datasets[1]?.data[r],meta2:e.data.datasets[1]?.label})),children:[a.jsx(A.q,{strokeDasharray:"3 3",opacity:.2}),a.jsx(k.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?`${e.substring(0,5)}...`:e}),a.jsx(w.B,{fontSize:10,tick:{fill:"currentColor"}}),a.jsx(E.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),a.jsx(R.D,{wrapperStyle:{fontSize:"10px"}}),a.jsx(T.$,{dataKey:"value",name:e.data.datasets[0].label,fill:e.data.datasets[0].backgroundColor,radius:[4,4,0,0]}),e.data.datasets[1]&&a.jsx(T.$,{dataKey:"value2",name:e.data.datasets[1].label,fill:e.data.datasets[1].backgroundColor,radius:[4,4,0,0]})]}):"line"===e.chartType?(0,a.jsxs)(ec,{data:e.data.datasets[0].data.map((t,r)=>({name:e.data.labels[r],value:t,meta:e.data.datasets[0].label,value2:e.data.datasets[1]?.data[r],meta2:e.data.datasets[1]?.label})),children:[a.jsx(A.q,{strokeDasharray:"3 3",opacity:.2}),a.jsx(k.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?`${e.substring(0,5)}...`:e}),a.jsx(w.B,{fontSize:10,tick:{fill:"currentColor"}}),a.jsx(E.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),a.jsx(R.D,{wrapperStyle:{fontSize:"10px"}}),a.jsx(el,{type:"monotone",dataKey:"value",name:e.data.datasets[0].label,stroke:e.data.datasets[0].backgroundColor,activeDot:{r:8},strokeWidth:2}),e.data.datasets[1]&&a.jsx(el,{type:"monotone",dataKey:"value2",name:e.data.datasets[1].label,stroke:e.data.datasets[1].backgroundColor,activeDot:{r:8},strokeWidth:2})]}):(0,a.jsxs)(eu.u,{children:[a.jsx(em.b,{data:e.data.datasets[0].data.map((t,r)=>({name:e.data.labels[r],value:t})),cx:"50%",cy:"50%",labelLine:!1,outerRadius:60,fill:"#8884d8",dataKey:"value",nameKey:"name",label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,children:e.data.datasets[0].data.map((e,t)=>a.jsx(ep.b,{fill:t<eb.length?eb[t]:eb[t%eb.length]},`cell-${t}`))}),a.jsx(E.u,{formatter:e=>[`${e}`,""],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),a.jsx(R.D,{wrapperStyle:{fontSize:"10px"}})]})})}):null},t)),!f&&ey[e]?.command&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx("div",{className:"h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center",children:a.jsx("span",{className:"text-xs text-white font-medium",children:e+1})}),a.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Digite um comando:"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-1 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-sm font-mono",children:a.jsx(v,{cursor:!0,sequence:[ey[e]?.command||""],wrapper:"span",speed:50,style:{display:"inline-block"},repeat:0,className:"text-gray-800 dark:text-gray-200"})}),a.jsx("button",{className:"ml-2 p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>D(),"aria-label":"Pr\xf3ximo comando",children:a.jsx(c.Z,{className:"h-4 w-4","aria-hidden":"true"})})]})]}),a.jsx("div",{"aria-live":"polite",className:"sr-only",children:f?"Comando digitado. Processando resultado.":""})]})]})})]})}function eN({data:e,chartType:t}){if(!e||!e.datasets||!e.datasets[0])return a.jsx("div",{className:"flex h-full items-center justify-center",children:a.jsx("p",{className:"text-xs text-gray-500",children:"Dados do gr\xe1fico indispon\xedveis"})});let{labels:r,datasets:s}=e,n=s[0],o=s[1];return(0,a.jsxs)("div",{className:"h-full w-full flex flex-col","aria-label":`Gr\xe1fico de ${t}`,role:"img",children:[a.jsx("div",{className:"text-xs font-medium mb-2 text-center",children:"bar"===t?"Gr\xe1fico de Barras":"line"===t?"Gr\xe1fico de Linha":"Gr\xe1fico de Pizza"}),a.jsx("div",{className:"flex-1 flex items-end space-x-1 px-2 overflow-hidden",children:r.map((e,t)=>{let r=Math.max(...n.data),s=Math.max(10,n.data[t]/r*100);return(0,a.jsxs)("div",{className:"flex flex-col items-center flex-1 min-w-0",children:[a.jsx("div",{className:"w-full relative rounded-t-sm focus:outline-none focus:ring-2 focus:ring-primary",style:{height:`${s}%`,backgroundColor:n.backgroundColor},"aria-label":`${e}: ${n.data[t]}`,tabIndex:0,role:"button",onKeyDown:r=>{("Enter"===r.key||" "===r.key)&&(r.preventDefault(),alert(`${e}: ${n.data[t]}`))}}),a.jsx("div",{className:"text-[8px] mt-1 truncate w-full text-center",title:e,children:e})]},t)})}),(0,a.jsxs)("div",{className:"flex justify-center mt-2 gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx("div",{className:"w-2 h-2",style:{backgroundColor:n.backgroundColor},"aria-hidden":"true"}),a.jsx("span",{className:"text-[9px]",children:n.label})]}),o&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx("div",{className:"w-2 h-2",style:{backgroundColor:o.backgroundColor},"aria-hidden":"true"}),a.jsx("span",{className:"text-[9px]",children:o.label})]})]})]})}},38443:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var a=r(10326),s=r(79360);r(17577);var n=r(51223);let o=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return a.jsx("div",{className:(0,n.cn)(o({variant:t}),e),...r})}},29752:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>d,SZ:()=>u,Zb:()=>l,aY:()=>m,eW:()=>p,ll:()=>c});var a=r(10326),s=r(31722),n=r(17577),o=r(45365),i=r(51223);let l=(0,n.forwardRef)(({className:e,children:t,hoverable:r=!1,variant:n="default",noPadding:l=!1,animated:d=!1,...c},u)=>{let m=(0,i.cn)("rounded-xl border shadow-sm",{"p-6":!l,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":r&&!d,"border-border bg-card":"default"===n,"border-border/50 bg-transparent":"outline"===n,"bg-card/90 backdrop-blur-md border-border/50":"glass"===n,"bg-gradient-primary text-primary-foreground border-none":"gradient"===n},e);return d?a.jsx(s.E.div,{ref:u,className:m,...(0,o.Ph)("card"),whileHover:r?o.q.hover:void 0,whileTap:r?o.q.tap:void 0,...c,children:t}):a.jsx("div",{ref:u,className:m,...c,children:t})});l.displayName="Card";let d=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("mb-4 flex flex-col space-y-1.5",e),...t}));d.displayName="CardHeader";let c=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("h3",{ref:r,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let u=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let m=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("card-content",e),...t}));m.displayName="CardContent";let p=(0,n.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("flex items-center pt-4 mt-auto",e),...t}));p.displayName="CardFooter"},2578:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>c,ZP:()=>u});var a=r(10326),s=r(91470),n=r(37202);let o=(0,r(76557).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var i=r(17577),l=r(51223);let d={error:{className:"bg-destructive/15 text-destructive",iconClassName:"text-destructive",icon:a.jsx(s.Z,{className:"h-5 w-5"})},warning:{className:"bg-warning/15 text-warning",iconClassName:"text-warning",icon:a.jsx(n.Z,{className:"h-5 w-5"})},info:{className:"bg-info/15 text-info",iconClassName:"text-info",icon:a.jsx(o,{className:"h-5 w-5"})},success:{className:"bg-success/15 text-success",iconClassName:"text-success",icon:a.jsx(o,{className:"h-5 w-5"})}},c=i.forwardRef(({type:e="error",message:t,description:r,className:s,iconClassName:n,_iconClassName:o,_icon:i},c)=>{let u=d[e],m=i||u.icon;return(0,a.jsxs)("div",{ref:c,className:(0,l.cn)("flex items-start gap-3 rounded-md border p-3",u.className,s),role:"alert",children:[a.jsx("div",{className:(0,l.cn)("mt-0.5 shrink-0",u.iconClassName,n||o),children:m}),(0,a.jsxs)("div",{className:"grid gap-1",children:[a.jsx("div",{className:"font-medium leading-none tracking-tight",children:t}),r&&a.jsx("div",{className:"text-sm opacity-80",children:r})]})]})});c.displayName="ErrorMessage";let u=c},62734:(e,t,r)=>{"use strict";r.d(t,{RM:()=>l,aF:()=>d});var a=r(17577),s=r.n(a),n=r(51223);let o={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},i={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function l(e="default",t="md",r=!1,a){return(0,n.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o[e],i[t],r&&"min-h-[80px] resize-vertical",a)}function d(e,t){return t?s().createElement("div",{className:t},e):e}},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>o,Z:()=>i});var a=r(10326),s=r(17577),n=r(62734);let o=s.forwardRef(({className:e,type:t,wrapperClassName:r,variant:s="default",fieldSize:o="md",inputSize:i,...l},d)=>{let c=a.jsx("input",{type:t,className:(0,n.RM)(s,i||o,!1,e),ref:d,...l});return(0,n.aF)(c,r)});o.displayName="Input";let i=o},50258:(e,t,r)=>{"use strict";r.d(t,{Kk:()=>p,MD:()=>m});var a=r(10326),s=r(17577),n=r.n(s);class o{constructor(){this.metrics=new Map,this.renderEvents=[],this.isEnabled=!1,this.isEnabled=!1}recordRender(e,t,r=!1,a){if(!this.isEnabled)return;let s=Date.now();this.renderEvents.push({componentName:e,timestamp:s,renderTime:t,props:a||{},isOptimized:r});let n=this.metrics.get(e);n?(n.renderCount++,n.lastRenderTime=t,n.totalRenderTime+=t,n.averageRenderTime=n.totalRenderTime/n.renderCount):this.metrics.set(e,{componentName:e,renderCount:1,lastRenderTime:t,averageRenderTime:t,totalRenderTime:t,isOptimized:r}),this.renderEvents.length>1e3&&(this.renderEvents=this.renderEvents.slice(-1e3))}getComponentMetrics(e){return this.metrics.get(e)}getAllMetrics(){return Array.from(this.metrics.values())}getRecentRenderEvents(e=100){return this.renderEvents.slice(-e)}getPerformanceComparison(){let e=this.getAllMetrics().filter(e=>e.isOptimized),t=this.getAllMetrics().filter(e=>!e.isOptimized),r=e.reduce((e,t)=>e+t.averageRenderTime,0)/e.length||0,a=t.reduce((e,t)=>e+t.averageRenderTime,0)/t.length||0,s=e.reduce((e,t)=>e+t.renderCount,0)/e.length||0,n=t.reduce((e,t)=>e+t.renderCount,0)/t.length||0;return{optimized:e,nonOptimized:t,improvement:{averageRenderTimeReduction:a>0?(a-r)/a*100:0,renderCountReduction:n>0?(n-s)/n*100:0}}}generateReport(){let e=this.getPerformanceComparison(),t=this.metrics.size,r=e.optimized.length,a=e.nonOptimized.length;return`
📊 RELAT\xd3RIO DE PERFORMANCE - COMPONENTES UI

📈 Resumo Geral:
- Total de componentes monitorados: ${t}
- Componentes otimizados: ${r}
- Componentes n\xe3o otimizados: ${a}

🚀 Melhorias de Performance:
- Redu\xe7\xe3o m\xe9dia no tempo de renderiza\xe7\xe3o: ${e.improvement.averageRenderTimeReduction.toFixed(2)}%
- Redu\xe7\xe3o m\xe9dia no n\xfamero de renderiza\xe7\xf5es: ${e.improvement.renderCountReduction.toFixed(2)}%

🔝 Top 5 Componentes Mais Renderizados:
${this.getAllMetrics().sort((e,t)=>t.renderCount-e.renderCount).slice(0,5).map((e,t)=>`${t+1}. ${e.componentName}: ${e.renderCount} renders (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}

⚡ Top 5 Componentes Mais Lentos:
${this.getAllMetrics().sort((e,t)=>t.averageRenderTime-e.averageRenderTime).slice(0,5).map((e,t)=>`${t+1}. ${e.componentName}: ${e.averageRenderTime.toFixed(2)}ms (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}
    `.trim()}clear(){this.metrics.clear(),this.renderEvents=[]}exportData(){let e=this.getPerformanceComparison();return{metrics:this.getAllMetrics(),events:this.renderEvents,summary:e}}}let i=new o;function l(e,t=!1){return n().useRef(0),{recordCustomMetric:(r,a)=>{i.recordRender(`${e}.${r}`,a,t)}}}var d=r(91664);function c(e,t){for(let r of["variant","size","disabled","children","className","animated","icon","iconPosition","asChild"])if(e[r]!==t[r])return!1;if("object"==typeof e.children&&"object"==typeof t.children)return e.children===t.children;for(let r of["onClick","onMouseEnter","onMouseLeave","onFocus","onBlur"]){let a=e[r],s=t[r];if((a||s)&&(!a||!s||a!==s))return!1}return!0}let u=(0,s.memo)(d.Button,c),m=n().forwardRef((e,t)=>(l("OptimizedButton",!0),a.jsx(u,{...e,ref:t})));m.displayName="OptimizedButton";let p=(0,s.memo)(({onAction:e,actionId:t,...r})=>{l("ActionButton",!0);let s=n().useCallback(()=>{e()},[e]);return a.jsx(d.Button,{...r,onClick:s})},(e,t)=>c(e,t)&&e.actionId===t.actionId&&e.onAction===t.onAction);p.displayName="ActionButton"},3236:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var a=r(10326),s=r(16999),n=r(17577),o=r(51223);let i=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsxs)(s.fC,{ref:n,className:(0,o.cn)("relative overflow-hidden",e),...r,children:[a.jsx(s.l_,{className:"h-full w-full rounded-[inherit]",children:t}),a.jsx(l,{}),a.jsx(s.Ns,{})]}));i.displayName=s.fC.displayName;let l=n.forwardRef(({className:e,orientation:t="vertical",...r},n)=>a.jsx(s.gb,{ref:n,orientation:t,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 border-t border-t-transparent p-[1px]",e),...r,children:a.jsx(s.q4,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=s.gb.displayName},82015:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(10326),s=r(17577),n=r(62734);let o=s.forwardRef(({className:e,wrapperClassName:t,variant:r="default",fieldSize:s="md",textareaSize:o,...i},l)=>{let d=a.jsx("textarea",{className:(0,n.RM)(r,o||s,!0,e),ref:l,...i});return(0,n.aF)(d,t)});o.displayName="Textarea";let i=o},82631:(e,t,r)=>{"use strict";r.d(t,{_v:()=>c,aJ:()=>d,pn:()=>i,u:()=>l});var a=r(10326),s=r(39313),n=r(17577),o=r(51223);let i=s.zt,l=s.fC,d=s.xz,c=n.forwardRef(({className:e,sideOffset:t=4,...r},n)=>a.jsx(s.VY,{ref:n,sideOffset:t,className:(0,o.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=s.VY.displayName},56627:(e,t,r)=>{"use strict";r.d(t,{V6:()=>p,pm:()=>x,s6:()=>m});var a=r(17577);let s={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},n=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:s.REMOVE_TOAST,toastId:e})},1e6);o.set(e,t)},l=(e,t)=>{switch(t.type){case s.ADD_TOAST:return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case s.UPDATE_TOAST:return{...e,toasts:e.toasts.map(e=>e.id===t.toast?.id?{...e,...t.toast}:e)};case s.DISMISS_TOAST:{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case s.REMOVE_TOAST:if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};default:return e}},d=[],c={toasts:[]};function u(e){c=l(c,e),d.forEach(e=>{e(c)})}function m({...e}){let t=(n=(n+1)%Number.MAX_VALUE).toString(),r=()=>u({type:s.DISMISS_TOAST,toastId:t});return u({type:s.ADD_TOAST,toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:s.UPDATE_TOAST,toast:{...e,id:t}})}}function p(){let[e,t]=a.useState(c);return a.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{toast:m,dismiss:e=>u({type:s.DISMISS_TOAST,toastId:void 0===e?"":e}),toasts:e.toasts}}let x=p},35342:(e,t,r)=>{"use strict";var a,s,n,o,i;r.d(t,{ox:()=>a}),function(e){e.FORMULA="FORMULA",e.FILTER="FILTER",e.SORT="SORT",e.FORMAT="FORMAT",e.CHART="CHART",e.CELL_UPDATE="CELL_UPDATE",e.COLUMN_OPERATION="COLUMN_OPERATION",e.ROW_OPERATION="ROW_OPERATION",e.TABLE="TABLE",e.DATA_TRANSFORMATION="DATA_TRANSFORMATION",e.PIVOT_TABLE="PIVOT_TABLE",e.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",e.ADVANCED_CHART="ADVANCED_CHART",e.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",e.RANGE_UPDATE="RANGE_UPDATE",e.CELL_MERGE="CELL_MERGE",e.CELL_SPLIT="CELL_SPLIT",e.NAMED_RANGE="NAMED_RANGE",e.VALIDATION="VALIDATION",e.FREEZE_PANES="FREEZE_PANES",e.SHEET_OPERATION="SHEET_OPERATION",e.ANALYSIS="ANALYSIS",e.GENERIC="GENERIC"}(a||(a={})),function(e){e.LINE="LINE",e.BAR="BAR",e.COLUMN="COLUMN",e.AREA="AREA",e.SCATTER="SCATTER",e.PIE="PIE"}(s||(s={})),function(e){e.EQUALS="equals",e.NOT_EQUALS="notEquals",e.GREATER_THAN="greaterThan",e.LESS_THAN="lessThan",e.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",e.LESS_THAN_OR_EQUAL="lessThanOrEqual",e.CONTAINS="contains",e.NOT_CONTAINS="notContains",e.BEGINS_WITH="beginsWith",e.ENDS_WITH="endsWith",e.BETWEEN="between"}(n||(n={})),function(e){e.DISCONNECTED="disconnected",e.CONNECTING="connecting",e.CONNECTED="connected",e.ERROR="error"}(o||(o={})),function(e){e.FORMULA_ERROR="FORMULA_ERROR",e.REFERENCE_ERROR="REFERENCE_ERROR",e.VALUE_ERROR="VALUE_ERROR",e.NAME_ERROR="NAME_ERROR",e.RANGE_ERROR="RANGE_ERROR",e.SYNTAX_ERROR="SYNTAX_ERROR",e.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",e.FORMAT_ERROR="FORMAT_ERROR",e.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",e.UNKNOWN_ERROR="UNKNOWN_ERROR"}(i||(i={}))},36478:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var a=r(71159),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(e,t)=>{let r=(0,a.forwardRef)(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,children:d,...c},u)=>(0,a.createElement)("svg",{ref:u,...s,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:`lucide lucide-${n(e)}`,...c},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...(Array.isArray(d)?d:[d])||[]]));return r.displayName=`${e}`,r}},44880:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(19510),s=r(36478);let n=(0,s.Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),o=(0,s.Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),i=(0,s.Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]),l=(0,s.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),d=(0,s.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),c=(0,s.Z)("FileSpreadsheet",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M14 17h2",key:"10kma7"}]]),u=(0,s.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var m=r(57371),p=r(71159),x=r(68570);let f=(0,x.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\command-examples-wrapper.tsx`),{__esModule:h,$$typeof:g}=f;f.default;let b=(0,x.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\command-examples-wrapper.tsx#CommandExamplesWrapper`);var y=r(27039),v=r(40644);let N=(0,p.lazy)(()=>r.e(7249).then(r.bind(r,7249)).then(e=>({default:e.HeroSection}))),j={blue:{bgFrom:"from-blue-100",bgTo:"to-blue-200",darkFrom:"dark:from-blue-900/50",darkTo:"dark:to-blue-800/30",text:"text-blue-600",darkText:"dark:text-blue-400"},indigo:{bgFrom:"from-indigo-100",bgTo:"to-indigo-200",darkFrom:"dark:from-indigo-900/50",darkTo:"dark:to-indigo-800/30",text:"text-indigo-600",darkText:"dark:text-indigo-400"},purple:{bgFrom:"from-purple-100",bgTo:"to-purple-200",darkFrom:"dark:from-purple-900/50",darkTo:"dark:to-purple-800/30",text:"text-purple-600",darkText:"dark:text-purple-400"},green:{bgFrom:"from-green-100",bgTo:"to-green-200",darkFrom:"dark:from-green-900/50",darkTo:"dark:to-green-800/30",text:"text-green-600",darkText:"dark:text-green-400"},amber:{bgFrom:"from-amber-100",bgTo:"to-amber-200",darkFrom:"dark:from-amber-900/50",darkTo:"dark:to-amber-800/30",text:"text-amber-600",darkText:"dark:text-amber-400"},rose:{bgFrom:"from-rose-100",bgTo:"to-rose-200",darkFrom:"dark:from-rose-900/50",darkTo:"dark:to-rose-800/30",text:"text-rose-600",darkText:"dark:text-rose-400"}};function A(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden bg-gradient-to-b from-blue-50 via-indigo-50/50 to-white dark:from-gray-900 dark:via-blue-950/20 dark:to-background pb-12",children:[(0,a.jsxs)("div",{className:"absolute inset-0 pointer-events-none overflow-hidden",children:[a.jsx("div",{className:"absolute -top-[30%] -left-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-600/5 blur-3xl opacity-70"}),a.jsx("div",{className:"absolute -bottom-[20%] right-[10%] w-[40%] h-[40%] rounded-full bg-gradient-to-bl from-indigo-400/20 to-blue-600/5 blur-3xl opacity-70"})]}),a.jsx("div",{className:"container relative mx-auto px-4 pt-16 pb-20 max-w-6xl z-10",children:a.jsx(p.Suspense,{fallback:a.jsx("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx("div",{className:"w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"}),a.jsx("p",{className:"mt-4 text-sm text-muted-foreground",children:"Carregando..."})]})}),children:a.jsx(N,{})})})]}),a.jsx("section",{className:"py-12 bg-gradient-to-b from-white to-blue-50/50 dark:from-background dark:to-blue-950/10",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 max-w-6xl",children:[(0,a.jsxs)("div",{className:"text-center mb-10",children:[a.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-2",children:"Transforme sua experi\xeancia com planilhas"}),a.jsx("p",{className:"text-base text-muted-foreground max-w-2xl mx-auto",children:"IA avan\xe7ada que simplifica sua intera\xe7\xe3o com dados"})]}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-12",children:[{icon:a.jsx(n,{className:"h-5 w-5","aria-hidden":"true"}),title:"Linguagem natural",description:"Use comandos em portugu\xeas simples",color:"blue"},{icon:a.jsx(o,{className:"h-5 w-5","aria-hidden":"true"}),title:"Planilhas complexas",description:"Manipule dados facilmente",color:"indigo"},{icon:a.jsx(i,{className:"h-5 w-5","aria-hidden":"true"}),title:"Visualiza\xe7\xf5es",description:"Gr\xe1ficos profissionais r\xe1pidos",color:"purple"},{icon:a.jsx(l,{className:"h-5 w-5","aria-hidden":"true"}),title:"Importa\xe7\xe3o/exporta\xe7\xe3o",description:"Compatibilidade com Excel",color:"green"},{icon:a.jsx(d,{className:"h-5 w-5","aria-hidden":"true"}),title:"F\xf3rmulas inteligentes",description:"Fun\xe7\xf5es avan\xe7adas simplificadas",color:"amber"},{icon:a.jsx(c,{className:"h-5 w-5","aria-hidden":"true"}),title:"Templates prontos",description:"Modelos para iniciar rapidamente",color:"rose"}].map((e,t)=>{let r=j[e.color];return(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm hover:shadow-sm transition-all focus-within:ring-2 focus-within:ring-primary/70",tabIndex:0,role:"button","aria-label":`Feature: ${e.title} - ${e.description}`,children:[a.jsx("div",{className:(0,v.cn)("p-2 rounded-lg flex items-center justify-center bg-gradient-to-br",r.bgFrom,r.bgTo,r.darkFrom,r.darkTo,r.text,r.darkText),children:e.icon}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-semibold",children:e.title}),a.jsx("p",{className:"text-xs text-muted-foreground hidden xs:block",children:e.description})]})]},t)})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-900/80 rounded-xl p-6 shadow-md border border-gray-200 dark:border-gray-800",children:[a.jsx("h3",{className:"text-xl font-bold mb-4 text-center",children:"Como Funciona"}),a.jsx("div",{className:"grid md:grid-cols-3 gap-4",children:[{step:"1",title:"Descreva o que precisa",description:"Use linguagem natural para pedir o que deseja."},{step:"2",title:"A IA cria em segundos",description:"O Excel Copilot interpreta seu pedido e cria tudo automaticamente."},{step:"3",title:"Personalize e exporte",description:"Ajuste conforme necess\xe1rio e exporte como arquivo Excel."}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[a.jsx("div",{className:"flex-shrink-0 flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full shadow-sm text-sm font-bold","aria-hidden":"true",children:e.step}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-semibold mb-1",children:[(0,a.jsxs)("span",{className:"sr-only",children:["Passo ",e.step,": "]}),e.title]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))})]})]})}),a.jsx("section",{className:"py-8 sm:py-12",id:"exemplos","aria-labelledby":"exemplos-titulo",children:(0,a.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 max-w-6xl",children:[(0,a.jsxs)("div",{className:"text-center mb-4 sm:mb-6",children:[a.jsx("h2",{id:"exemplos-titulo",className:"text-xl sm:text-2xl md:text-3xl font-bold mb-1 sm:mb-2",children:"Experimente esses comandos"}),a.jsx("p",{className:"text-sm sm:text-base text-muted-foreground max-w-2xl mx-auto",children:"Veja o poder da linguagem natural em a\xe7\xe3o"})]}),a.jsx("div",{className:"bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-gray-200 dark:border-gray-800 rounded-xl shadow-md p-2 sm:p-4",children:a.jsx(p.Suspense,{fallback:a.jsx("div",{className:"h-[200px] sm:h-[250px] flex items-center justify-center","aria-live":"polite","aria-busy":"true",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2 sm:gap-3",children:[a.jsx("div",{className:"w-6 sm:w-8 h-6 sm:h-8 rounded-full border-3 border-blue-200 border-t-blue-600 animate-spin",role:"status"}),a.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground",children:"Carregando exemplos..."})]})}),children:a.jsx(b,{})})}),a.jsx("div",{className:"mt-6 sm:mt-8 bg-gradient-to-br from-blue-600 to-indigo-700 dark:from-blue-700 dark:to-indigo-900 rounded-xl overflow-hidden shadow-lg p-4 sm:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-3 sm:gap-4",children:[a.jsx("div",{className:"p-2 sm:p-3 bg-white/20 backdrop-blur-sm rounded-full","aria-hidden":"true",children:a.jsx(c,{className:"h-6 w-6 sm:h-8 sm:w-8 text-white"})}),(0,a.jsxs)("div",{className:"text-center md:text-left flex-1",children:[a.jsx("h2",{className:"text-lg sm:text-xl md:text-2xl font-bold text-white mb-2",children:"Comece sua jornada agora"}),a.jsx("p",{className:"text-xs sm:text-sm text-blue-100 mb-3 sm:mb-4 max-w-2xl",children:"Pare de perder tempo com f\xf3rmulas complexas. Use o poder da IA agora."}),a.jsx(y.z,{className:"rounded-full bg-white hover:bg-gray-100 text-blue-600 px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-0 shadow-md focus:ring-2 focus:ring-white/70 focus:ring-offset-2 focus:ring-offset-blue-600",asChild:!0,children:(0,a.jsxs)(m.default,{href:"/dashboard",children:["Criar minha primeira planilha"," ",a.jsx(u,{className:"ml-1 h-3 w-3 sm:h-4 sm:w-4","aria-hidden":"true"})]})})]})]})})]})})]})}},37125:(e,t,r)=>{"use strict";function a(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:()=>a})},6009:(e,t,r)=>{"use strict";r.d(t,{C2:()=>o,TX:()=>i,fC:()=>l});var a=r(17577),s=r(45226),n=r(10326),o=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=a.forwardRef((e,t)=>(0,n.jsx)(s.WV.span,{...e,ref:t,style:{...o,...e.style}}));i.displayName="VisuallyHidden";var l=i}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,9557,7410,86,7915,6068,615,2972,4433,6841],()=>r(63021));module.exports=a})();