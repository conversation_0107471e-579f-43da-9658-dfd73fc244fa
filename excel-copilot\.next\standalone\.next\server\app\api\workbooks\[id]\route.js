"use strict";(()=>{var e={};e.id=4754,e.ids=[4754],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},47606:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>b,patchFetch:()=>g,requestAsyncStorage:()=>w,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>k});var t={};o.r(t),o.d(t,{DELETE:()=>h,GET:()=>p,PUT:()=>x,dynamic:()=>d,runtime:()=>l});var a=o(49303),s=o(88716),n=o(60670),i=o(87070),u=o(45609),c=o(63841);let d="force-dynamic",l="nodejs";async function p(e,{params:r}){try{let e=r.id;if(!e)return i.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let o=await (0,u.getServerSession)();if(!o?.user)return i.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let t=o.user.id,a=await c.prisma.workbook.findUnique({where:{id:e},include:{sheets:!0}});if(!a)return i.NextResponse.json({error:"Planilha n\xe3o encontrada"},{status:404});if(a.userId!==t&&!a.isPublic&&!await c.prisma.workbookShare.findFirst({where:{workbookId:e,sharedWithUserId:t}}))return i.NextResponse.json({error:"Acesso negado"},{status:403});return await c.prisma.workbook.update({where:{id:e},data:{lastAccessedAt:new Date}}),i.NextResponse.json({workbook:a})}catch(e){return console.error("Erro ao buscar workbook:",e),i.NextResponse.json({error:"Erro ao carregar planilha"},{status:500})}}async function x(e,{params:r}){try{let o=r.id;if(!o)return i.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let t=await (0,u.getServerSession)();if(!t?.user)return i.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let a=t.user.id;if(!await c.prisma.workbook.findFirst({where:{id:o,userId:a}}))return i.NextResponse.json({error:"Planilha n\xe3o encontrada ou sem permiss\xe3o"},{status:404});let{name:s,description:n,isPublic:d}=await e.json();if(!s)return i.NextResponse.json({error:"Nome da planilha \xe9 obrigat\xf3rio"},{status:400});let l=await c.prisma.workbook.update({where:{id:o},data:{name:s,description:n,isPublic:d??!1}});return i.NextResponse.json({workbook:l})}catch(e){return console.error("Erro ao atualizar workbook:",e),i.NextResponse.json({error:"Erro ao atualizar planilha"},{status:500})}}async function h(e,{params:r}){try{let e=r.id;if(!e)return i.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let o=await (0,u.getServerSession)();if(!o?.user)return i.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let t=o.user.id;if(!await c.prisma.workbook.findFirst({where:{id:e,userId:t}}))return i.NextResponse.json({error:"Planilha n\xe3o encontrada ou sem permiss\xe3o"},{status:404});return await c.prisma.workbook.delete({where:{id:e}}),i.NextResponse.json({message:"Planilha exclu\xedda com sucesso"})}catch(e){return console.error("Erro ao excluir workbook:",e),i.NextResponse.json({error:"Erro ao excluir planilha"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/workbooks/[id]/route",pathname:"/api/workbooks/[id]",filename:"route",bundlePath:"app/api/workbooks/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:w,staticGenerationAsyncStorage:k,serverHooks:f}=m,b="/api/workbooks/[id]/route";function g(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:k})}},63841:(e,r,o)=>{o.d(r,{P:()=>u,prisma:()=>i});var t=o(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},n=[],i=global.prisma||new t.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function u(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function c(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{s.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),s.averageQueryTime=n.reduce((e,r)=>e+r,0)/n.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8948,5972,330,5609],()=>o(47606));module.exports=t})();