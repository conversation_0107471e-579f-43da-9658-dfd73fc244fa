"use strict";(()=>{var e={};e.id=4017,e.ids=[4017],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},12121:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>y,routeModule:()=>w,serverHooks:()=>k,staticGenerationAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{GET:()=>b,dynamic:()=>f,runtime:()=>m});var n=r(49303),a=r(88716),s=r(60670),i=r(87070),u=r(7410),c=r(52972),p=r(95456),d=r(43895),l=r(63841);let f="force-dynamic",m="nodejs",x=u.z.object({limit:u.z.coerce.number().int().min(1).max(50).default(10),page:u.z.coerce.number().int().min(0).default(0)});async function b(e){try{let t=e.nextUrl.searchParams,r=x.safeParse({limit:t.get("limit"),page:t.get("page")}),{limit:o,page:n}=r.success?r.data:{limit:10,page:0},a=await (0,p.rc)();if(!a){if(c.Vi.IS_DEVELOPMENT&&c.Vi.FEATURES?.SKIP_AUTH_PROVIDERS)return d.kg.warn("Permitindo acesso sem autentica\xe7\xe3o em modo de desenvolvimento"),i.NextResponse.json({workbooks:[{id:"dev-1",name:"Planilha de Demonstra\xe7\xe3o Recente 1",createdAt:new Date,updatedAt:new Date,lastAccessedAt:new Date,sheets:[{id:"sheet-1"}]},{id:"dev-2",name:"Planilha de Demonstra\xe7\xe3o Recente 2",createdAt:new Date,updatedAt:new Date,lastAccessedAt:new Date(Date.now()-864e5),sheets:[{id:"sheet-2"}]}],pagination:{limit:o,page:n,totalItems:2,totalPages:1,hasMore:!1}});return i.NextResponse.json({error:"N\xe3o autorizado",details:"Usu\xe1rio n\xe3o autenticado ou sess\xe3o inv\xe1lida"},{status:401})}let s=await l.prisma.workbook.count({where:{userId:a}}),u=Math.ceil(s/o),f=n<u-1,m=(await l.prisma.workbook.findMany({where:{userId:a},orderBy:{updatedAt:"desc"},take:o,skip:n*o,include:{sheets:{select:{id:!0}}}})).map(e=>({id:e.id,name:e.name,createdAt:e.createdAt,updatedAt:e.updatedAt,lastAccessedAt:e.updatedAt,sheets:e.sheets}));return i.NextResponse.json({workbooks:m,pagination:{limit:o,page:n,totalItems:s,totalPages:u,hasMore:f}})}catch(e){return d.kg.error("Erro ao listar workbooks recentes:",e),i.NextResponse.json({error:"Erro ao buscar planilhas recentes",details:e instanceof Error?e.message:String(e)},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/recent/route",pathname:"/api/workbooks/recent",filename:"route",bundlePath:"app/api/workbooks/recent/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\recent\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:y,staticGenerationAsyncStorage:h,serverHooks:k}=w,g="/api/workbooks/recent/route";function v(){return(0,s.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:h})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var n=r(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=n?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(o,a,i):o[a]=e[a]}return o.default=e,r&&r.set(e,o),o}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},95456:(e,t,r)=>{r.d(t,{Lz:()=>n.L,rc:()=>s});var o=r(75571),n=r(81628);async function a(){return await (0,o.getServerSession)(n.L)}async function s(){let e=await a();return e?.user?.id||null}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>r(12121));module.exports=o})();