"use strict";(()=>{var e={};e.id=789,e.ids=[789],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},7456:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>x});var t={};o.r(t),o.d(t,{POST:()=>d});var s=o(49303),a=o(88716),n=o(60670),i=o(87070),u=o(45609),c=o(63841);async function d(e,{params:r}){try{let e=r.id;if(!e)return i.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let o=await (0,u.getServerSession)();if(!o||!o.user)return i.NextResponse.json({error:"N\xe3o autorizado"},{status:401});let t=o.user.id,s=await c.prisma.workbook.findUnique({where:{id:e},include:{sheets:!0}});if(!s)return i.NextResponse.json({error:"Planilha n\xe3o encontrada"},{status:404});if(s.userId!==t&&!s.isPublic)return i.NextResponse.json({error:"Acesso negado"},{status:403});let a=await c.prisma.workbook.create({data:{name:`${s.name} (c\xf3pia)`,description:s.description,userId:t,isPublic:!1}}),n=[];for(let e of s.sheets){let r=await c.prisma.sheet.create({data:{name:e.name,workbookId:a.id,data:e.data}});n.push(r)}return i.NextResponse.json({workbook:{...a,sheets:n}},{status:201})}catch(e){return console.error("Erro ao duplicar workbook:",e),i.NextResponse.json({error:"Erro ao duplicar planilha"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/[id]/duplicate/route",pathname:"/api/workbooks/[id]/duplicate",filename:"route",bundlePath:"app/api/workbooks/[id]/duplicate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\duplicate\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:l,staticGenerationAsyncStorage:x,serverHooks:m}=p,h="/api/workbooks/[id]/duplicate/route";function f(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:x})}},63841:(e,r,o)=>{o.d(r,{P:()=>u,prisma:()=>i});var t=o(53524);let s={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},n=[],i=global.prisma||new t.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function u(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function c(){try{await i.$disconnect(),s.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){s.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{a.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),a.averageQueryTime=n.reduce((e,r)=>e+r,0)/n.length),e.duration&&e.duration>500&&s.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),s.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8948,5972,330,5609],()=>o(7456));module.exports=t})();