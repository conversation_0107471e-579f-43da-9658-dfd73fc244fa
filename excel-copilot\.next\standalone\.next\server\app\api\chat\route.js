"use strict";(()=>{var e={};e.id=744,e.ids=[744],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},54005:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>k,patchFetch:()=>P,requestAsyncStorage:()=>w,routeModule:()=>R,serverHooks:()=>q,staticGenerationAsyncStorage:()=>N});var o,a={};r.r(a),r.d(a,{POST:()=>S,dynamic:()=>T,runtime:()=>O});var s=r(49303),n=r(88716),i=r(60670),c=r(87070),u=r(45609),l=r(57147),d=r(71017);let p=require("@google-cloud/vertexai");var m=r(7410),h=r(52972),g=r(43895);!function(e){e.TIMEOUT="timeout",e.API_UNAVAILABLE="api_unavailable",e.INVALID_REQUEST="invalid_request",e.CONTENT_FILTERED="content_filtered",e.RATE_LIMITED="rate_limited",e.UNKNOWN="unknown",e.INVALID_RESPONSE_FORMAT="invalid_response_format",e.CONTEXT_LIMIT_EXCEEDED="context_limit_exceeded",e.TOKEN_LIMIT_EXCEEDED="token_limit_exceeded",e.RETRY_FAILED="retry_failed"}(o||(o={}));class f extends Error{constructor(e,t="unknown",r,o=!1){super(e),this.name="GeminiServiceError",this.type=t,this.details=r,this.recoverable=o}}let x=`
Voc\xea \xe9 um assistente especializado em Excel, capaz de ajudar a realizar opera\xe7\xf5es com planilhas.
Para todas as perguntas, forne\xe7a respostas concisas e diretas.
Quando o usu\xe1rio solicitar uma opera\xe7\xe3o no Excel, interprete o comando e responda com:

1. Uma explica\xe7\xe3o curta do que ser\xe1 feito
2. As opera\xe7\xf5es exatas a serem executadas no formato JSON

Quando responder com JSON, use o seguinte formato:
{
  "operations": [
    {
      "type": "TIPO_DA_OPERA\xc7\xc3O",
      "data": { ... par\xe2metros espec\xedficos ... }
    }
  ],
  "explanation": "Breve explica\xe7\xe3o do que foi feito"
}

Tipos de opera\xe7\xf5es dispon\xedveis:
- FORMULA: Para aplicar f\xf3rmulas em c\xe9lulas
- FILTER: Para filtrar dados
- SORT: Para ordenar dados
- CHART: Para criar ou modificar gr\xe1ficos
- COLUMN_OPERATION: Para opera\xe7\xf5es com colunas
- CONDITIONAL_FORMAT: Para formata\xe7\xe3o condicional
- PIVOT_TABLE: Para criar ou modificar tabelas din\xe2micas
- ADVANCED_VISUALIZATION: Para visualiza\xe7\xf5es avan\xe7adas
- DATA_ANALYSIS: Para an\xe1lise estat\xedstica de dados

Seja preciso na interpreta\xe7\xe3o dos comandos do usu\xe1rio e forne\xe7a os par\xe2metros corretos para cada tipo de opera\xe7\xe3o.
`;class E{constructor(){if(this.vertexAI=null,this.vertexModel=null,this.requestCache=new Map,this.requestsInProgress=new Map,this.cacheCleanupInterval=null,this.requestCount=0,this.errorCount=0,this.cacheHitCount=0,h.Vi.FEATURES.USE_MOCK_AI){g.kg.info("Usando modo mock para respostas de IA."),this.cacheCleanupInterval=setInterval(()=>this.cleanupCache(),36e5);return}h.Vi.VERTEX_AI.ENABLED&&this.initVertexAI()?g.kg.info("Servi\xe7o Vertex AI inicializado com sucesso"):g.kg.warn("Vertex AI n\xe3o p\xf4de ser inicializado e o modo mock est\xe1 desativado. O servi\xe7o de IA n\xe3o estar\xe1 dispon\xedvel."),this.cacheCleanupInterval=setInterval(()=>this.cleanupCache(),36e5)}initVertexAI(){try{let e=h.Vi.VERTEX_AI.PROJECT_ID,t=null;if(process.env.VERTEX_AI_CREDENTIALS)try{if(!(t=JSON.parse(process.env.VERTEX_AI_CREDENTIALS)).type||"service_account"!==t.type)return g.kg.error("Credenciais devem ser de uma service account"),!1;t.project_id&&!e&&(e=t.project_id),g.kg.info("Usando credenciais do Vertex AI da vari\xe1vel de ambiente")}catch(e){return g.kg.error("Erro ao analisar VERTEX_AI_CREDENTIALS:",e),!1}if(h.Vi.VERTEX_AI.CREDENTIALS_PATH){if(!l.existsSync(h.Vi.VERTEX_AI.CREDENTIALS_PATH))return g.kg.error(`Arquivo de credenciais do Vertex AI n\xe3o encontrado em: ${h.Vi.VERTEX_AI.CREDENTIALS_PATH}`),!1;process.env.GOOGLE_APPLICATION_CREDENTIALS=h.Vi.VERTEX_AI.CREDENTIALS_PATH,g.kg.info(`Usando credenciais do caminho configurado: ${h.Vi.VERTEX_AI.CREDENTIALS_PATH}`)}else if(t){let e=d.join(process.cwd(),".vertex-credentials-temp.json");l.writeFileSync(e,JSON.stringify(t,null,2)),process.env.GOOGLE_APPLICATION_CREDENTIALS=e,g.kg.info("Usando credenciais tempor\xe1rias do Vertex AI")}else{let e=d.join(process.cwd(),"vertex-credentials.json");if(l.existsSync(e))process.env.GOOGLE_APPLICATION_CREDENTIALS=e,g.kg.info(`Usando arquivo de credenciais padr\xe3o: ${e}`);else if(!process.env.GOOGLE_APPLICATION_CREDENTIALS)return g.kg.error("Nenhum arquivo de credenciais do Vertex AI encontrado e vari\xe1vel GOOGLE_APPLICATION_CREDENTIALS n\xe3o est\xe1 definida"),!1}if(!e&&process.env.GOOGLE_APPLICATION_CREDENTIALS&&l.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS))try{e=JSON.parse(l.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS,"utf8")).project_id,g.kg.info(`Project ID extra\xeddo das credenciais: ${e}`)}catch(e){return g.kg.error("Erro ao analisar arquivo de credenciais:",e),!1}if(!e)return g.kg.error("Project ID do Vertex AI n\xe3o encontrado nas credenciais ou configura\xe7\xf5es"),!1;let r=h.Vi.VERTEX_AI.LOCATION||"us-central1";this.vertexAI=new p.VertexAI({project:e,location:r});let o=h.Vi.VERTEX_AI.MODEL_NAME||"gemini-1.5-pro";return this.vertexModel=this.vertexAI.preview.getGenerativeModel({model:o}),g.kg.info(`Vertex AI inicializado com sucesso: projeto=${e}, regi\xe3o=${r}, modelo=${o}`),!0}catch(e){return g.kg.error("Erro ao inicializar Vertex AI:",e),!1}}static getInstance(){return E.instance||(E.instance=new E),E.instance}clearCache(e){if(e)for(let[t,r]of this.requestCache)t.includes(`user:${e}:`)&&this.requestCache.delete(t);else this.requestCache.clear()}cleanupCache(){let e=Date.now(),t=1e3*h.Vi.CACHE.AI_CACHE_TTL;for(let[r,o]of this.requestCache)e-o.timestamp>t&&this.requestCache.delete(r)}async shutdown(){this.cacheCleanupInterval&&(clearInterval(this.cacheCleanupInterval),this.cacheCleanupInterval=null),this.requestCache.clear(),this.requestsInProgress.clear(),g.kg.info("Servi\xe7o Google Gemini desligado com sucesso")}generateCacheKey(e,t=[],r={}){let o=r.userId?`user:${r.userId}:`:"",a=t.map(e=>`${e.role}:${e.content}`).join("|"),s=JSON.stringify({temperature:r.temperature,responseStructure:r.responseStructure,excelContext:r.excelContext});return`${o}${e}|${a}|${s}`}async processCommand(e,t){try{let r=[];return t&&(r.push({role:"user",content:`Contexto adicional: ${t}`}),r.push({role:"model",content:"Entendido. Utilizarei este contexto para processar seu pr\xf3ximo comando."})),await this.sendMessage(e,r,{temperature:.2,responseStructure:{preferJson:!0},systemPrompt:x})}catch(e){if(g.kg.error("Erro ao processar comando Excel:",e),e instanceof f)throw e;throw new f("N\xe3o foi poss\xedvel processar o comando Excel","unknown",e)}}async streamMessage(e,t=[],r={}){try{if(h.Vi.FEATURES.USE_MOCK_AI)return this.createMockStream(e,r);if(this.vertexAI&&this.vertexModel)return this.streamVertexAIMessage(e,t,r);throw new f("Servi\xe7o de IA n\xe3o est\xe1 dispon\xedvel. Verifique a configura\xe7\xe3o do Vertex AI ou ative o modo mock.","api_unavailable")}catch(r){let e=r instanceof f?r:new f(r instanceof Error?r.message:"Erro desconhecido no servi\xe7o de streaming","unknown",r);g.kg.error("Erro ao processar streaming de IA:",{error:e.message,type:e.type,details:e.details});let t=new TextEncoder;return new ReadableStream({start(r){r.enqueue(t.encode(JSON.stringify({error:e.message,type:e.type}))),r.close()}})}}async sendMessage(e,t=[],r={}){try{let o=this.generateCacheKey(e,t,r);if(this.requestsInProgress.has(o))return await this.requestsInProgress.get(o);if(!r.bypassCache){let t=this.requestCache.get(o);if(t)return this.cacheHitCount++,g.kg.debug("Usando resposta em cache para a mensagem:",{message:e,cacheKey:o}),t.response}if(h.Vi.FEATURES.USE_MOCK_AI){let t=this.getMockResponse(e,r);return r.bypassCache||this.requestCache.set(o,{response:t,timestamp:Date.now()}),t}if(!this.vertexAI||!this.vertexModel)throw new f("Servi\xe7o de IA n\xe3o est\xe1 dispon\xedvel. Verifique a configura\xe7\xe3o do Vertex AI ou ative o modo mock.","api_unavailable");let a=this.makeVertexAIRequest(e,t,r);this.requestsInProgress.set(o,a);try{let e=await a;return r.bypassCache||this.requestCache.set(o,{response:e,timestamp:Date.now()}),e}finally{this.requestsInProgress.delete(o)}}catch(e){if(g.kg.error("Erro ao enviar mensagem para Vertex AI:",e),e instanceof f)throw e;throw new f("N\xe3o foi poss\xedvel enviar mensagem para o servi\xe7o de IA","unknown",e)}}async makeRequest(e,t=[],r={}){if(h.Vi.FEATURES.USE_MOCK_AI)return this.getMockResponse(e,r);if(this.vertexAI&&this.vertexModel)return this.makeVertexAIRequest(e,t,r);throw new f("Servi\xe7o de IA n\xe3o est\xe1 dispon\xedvel. Verifique a configura\xe7\xe3o do Vertex AI ou ative o modo mock.","api_unavailable")}async makeVertexAIRequest(e,t=[],r={}){try{if(!this.vertexModel)throw new f("Modelo Vertex AI n\xe3o inicializado","api_unavailable");let o=r.systemPrompt||x,a=r.excelContext,s="";a&&(s+="\n\nContexto da planilha atual:",a.activeSheet&&(s+=`
- Planilha ativa: ${a.activeSheet}`),a.selection&&(s+=`
- Sele\xe7\xe3o atual: ${a.selection}`),a.headers&&a.headers.length>0&&(s+=`
- Cabe\xe7alhos dispon\xedveis: ${a.headers.join(", ")}`),a.recentOperations&&a.recentOperations.length>0&&(s+="\n- Opera\xe7\xf5es recentes:",a.recentOperations.forEach(e=>{s+=`
  * ${e}`})));let n=[];for(let e of(n.push({role:"system",parts:[{text:o+s}]}),t))n.push({role:"user"===e.role?"user":"assistant",parts:[{text:e.content}]});n.push({role:"user",parts:[{text:e}]});let i={temperature:r.temperature||.4,topP:.95,topK:40,maxOutputTokens:4096},c=(await this.vertexModel.generateContent({contents:n,generationConfig:i,safetySettings:[{category:"HARM_CATEGORY_HARASSMENT",threshold:"BLOCK_ONLY_HIGH"},{category:"HARM_CATEGORY_HATE_SPEECH",threshold:"BLOCK_ONLY_HIGH"},{category:"HARM_CATEGORY_SEXUALLY_EXPLICIT",threshold:"BLOCK_ONLY_HIGH"},{category:"HARM_CATEGORY_DANGEROUS_CONTENT",threshold:"BLOCK_ONLY_HIGH"}]})).response.candidates[0].content.parts[0].text;return this.processResponse(c,r)}catch(r){this.errorCount++;let e="Erro desconhecido na comunica\xe7\xe3o com o Vertex AI",t="unknown";throw r.message.includes("deadline")||r.message.includes("timeout")?(e="A requisi\xe7\xe3o para o Vertex AI excedeu o tempo limite",t="timeout"):r.message.includes("safety")||r.message.includes("blocked")?(e="O conte\xfado foi filtrado por quest\xf5es de seguran\xe7a",t="content_filtered"):r.message.includes("rate")||r.message.includes("quota")?(e="Limite de requisi\xe7\xf5es excedido. Tente novamente mais tarde",t="rate_limited"):(r.message.includes("authentication")||r.message.includes("credentials"))&&(e="Erro de autentica\xe7\xe3o com o Vertex AI",t="api_unavailable"),new f(e,t,r)}}processResponse(e,t){if(this.containsUnsafeContent(e))throw new f("A resposta foi filtrada por poss\xedvel conte\xfado inadequado.","content_filtered",{responseText:e});if(t.responseStructure?.preferJson)try{let r=e.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/)||e.match(/(\{[\s\S]*?\})/);if(r&&r[1]){let e=r[1].trim(),o=JSON.parse(e);if(t.responseStructure.jsonSchema)try{let e=this.convertJsonSchemaToZod(t.responseStructure.jsonSchema).safeParse(o);if(!e.success)throw g.kg.warn("Valida\xe7\xe3o de schema JSON falhou:",{errors:e.error.format(),data:o}),new f("A resposta da IA n\xe3o est\xe1 no formato esperado.","invalid_response_format",{errors:e.error.format(),data:o});return JSON.stringify(e.data)}catch(e){g.kg.warn("Erro ao validar schema JSON:",{error:e,jsonData:o})}return e}}catch(t){g.kg.warn("Falha ao processar JSON na resposta:",{error:t,responseText:e})}return e.trim()}convertJsonSchemaToZod(e){if("object"===e.type){let t={};if(e.properties)for(let[r,o]of Object.entries(e.properties)){let a=this.convertJsonPropertyToZod(o);e.required?.includes(r)||(a=a.optional()),t[r]=a}return m.z.object(t)}if("array"===e.type){let t=e.items?this.convertJsonPropertyToZod(e.items):m.z.any();return m.z.array(t)}return m.z.any()}convertJsonPropertyToZod(e){switch(e.type){case"string":{let t=m.z.string();return void 0!==e.minLength&&(t=t.min(e.minLength)),void 0!==e.maxLength&&(t=t.max(e.maxLength)),e.pattern&&(t=t.regex(new RegExp(e.pattern))),t}case"number":case"integer":{let t="integer"===e.type?m.z.number().int():m.z.number();return void 0!==e.minimum&&(t=t.min(e.minimum)),void 0!==e.maximum&&(t=t.max(e.maximum)),t}case"boolean":return m.z.boolean();case"object":return this.convertJsonSchemaToZod(e);case"array":return m.z.array(e.items?this.convertJsonPropertyToZod(e.items):m.z.any());default:return m.z.any()}}containsUnsafeContent(e){return[/não posso (ajudar|responder|fornecer|compartilhar|discutir) (isso|tal conteúdo|tal solicitação)/i,/como modelo de IA, não posso/i,/I'm (unable|not able) to (provide|assist|respond|reply)/i,/I cannot (provide|assist|respond|reply)/i,/I don't (feel comfortable|think it's appropriate)/i].some(t=>t.test(e))}getMockResponse(e,t){let r=e.toLowerCase();return t?.responseStructure?.preferJson?r.includes("soma")||r.includes("somar")?JSON.stringify({operations:[{type:"FORMULA",data:{formula:"SUM",range:"A1:A10",targetCell:"A12"}}],explanation:"Somando valores de A1 at\xe9 A10 e colocando o resultado em A12."},null,2):r.includes("m\xe9dia")||r.includes("media")?JSON.stringify({operations:[{type:"FORMULA",data:{formula:"AVERAGE",range:"B1:B20",targetCell:"B22"}}],explanation:"Calculando a m\xe9dia dos valores de B1 at\xe9 B20 e colocando o resultado em B22."},null,2):r.includes("gr\xe1fico")||r.includes("grafico")?JSON.stringify({operations:[{type:"CHART",data:{chartType:"BAR",dataRange:"A1:B10",title:"Gr\xe1fico de barras",hasLegend:!0}}],explanation:"Criando um gr\xe1fico de barras com os dados de A1 at\xe9 B10."},null,2):r.includes("filtro")||r.includes("filtrar")?JSON.stringify({operations:[{type:"FILTER",data:{range:"A1:D20",column:"B",condition:"GREATER_THAN",value:100}}],explanation:"Aplicando filtro para mostrar apenas valores maiores que 100 na coluna B."},null,2):JSON.stringify({operations:[{type:"OPERATION",data:{action:"MOCK_RESPONSE",description:"Esta \xe9 uma resposta simulada para desenvolvimento."}}],explanation:"Resposta simulada para fins de desenvolvimento e teste."},null,2):r.includes("ol\xe1")||r.includes("ola")||r.includes("oi")||r.includes("hello")?"Ol\xe1! Sou o assistente Excel. Como posso ajudar voc\xea hoje?":r.includes("soma")||r.includes("somar")?"Para somar valores, voc\xea pode usar a fun\xe7\xe3o SUM(). Por exemplo, para somar os valores de A1 at\xe9 A10, use =SUM(A1:A10).":r.includes("m\xe9dia")||r.includes("media")?"Para calcular a m\xe9dia, voc\xea pode usar a fun\xe7\xe3o AVERAGE(). Por exemplo, para calcular a m\xe9dia dos valores de B1 at\xe9 B10, use =AVERAGE(B1:B10).":r.includes("gr\xe1fico")||r.includes("grafico")?"Para criar um gr\xe1fico, selecione os dados que deseja incluir no gr\xe1fico e depois v\xe1 para a aba 'Inserir' e escolha o tipo de gr\xe1fico desejado, como gr\xe1fico de barras, linhas ou pizza.":"Esta \xe9 uma resposta simulada para desenvolvimento e teste. Sua mensagem foi: "+e}getServiceStats(){return{requestCount:this.requestCount,errorCount:this.errorCount,cacheHitCount:this.cacheHitCount,cacheSize:this.requestCache.size,activeRequests:this.requestsInProgress.size}}createMockStream(e,t={}){let r=new TextEncoder,o=this.getMockResponse(e,t),a=[];for(let e=0;e<o.length;e+=10)a.push(o.slice(e,e+10));return new ReadableStream({async start(e){for(let t of a)e.enqueue(r.encode(t)),await new Promise(e=>setTimeout(e,50));e.close()}})}async streamVertexAIMessage(e,t=[],r={}){let o=new TextEncoder;if(!this.vertexModel)throw new f("Modelo Vertex AI n\xe3o inicializado","api_unavailable");try{let a=r.systemPrompt||x,s="";if(r.excelContext){let e=r.excelContext;s=`
Contexto atual do Excel:
${e.activeSheet?`- Planilha ativa: ${e.activeSheet}`:""}
${e.selection?`- Sele\xe7\xe3o atual: ${e.selection}`:""}
${e.headers&&e.headers.length>0?`- Cabe\xe7alhos dispon\xedveis: ${e.headers.join(", ")}`:""}
${e.recentOperations&&e.recentOperations.length>0?`- Opera\xe7\xf5es recentes: ${e.recentOperations.join(", ")}`:""}
`}let n=[{role:"user",parts:[{text:`${a}

${s}

${e}`}]}];if(t&&t.length>0)for(let e of t)n.push({role:"user"===e.role?"user":"model",parts:[{text:e.content}]});let i={temperature:r.temperature??.4,topK:40,topP:.95,maxOutputTokens:r.responseStructure?.preferConcise?1024:2048},c=[{category:"HARM_CATEGORY_HARASSMENT",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_HATE_SPEECH",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_SEXUALLY_EXPLICIT",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_DANGEROUS_CONTENT",threshold:"BLOCK_MEDIUM_AND_ABOVE"}];g.kg.debug("Iniciando streaming com Vertex AI",{messageLength:e.length});let u=Date.now(),l=this.vertexModel;return new ReadableStream({async start(t){try{for await(let e of(await l.generateContentStream({contents:n,generationConfig:i,safetySettings:c})).stream){let r=e.candidates[0]?.content?.parts[0]?.text||"";r&&t.enqueue(o.encode(r))}let r=Date.now()-u;g.kg.debug("Streaming do Vertex AI conclu\xeddo",{duration:r,messageLength:e.length}),t.close()}catch(e){g.kg.error("Erro durante streaming do Vertex AI",{error:e}),t.enqueue(o.encode(JSON.stringify({error:"Ocorreu um erro ao processar sua solicita\xe7\xe3o",type:"unknown"}))),t.error(e)}}})}catch(e){return new ReadableStream({start(t){t.enqueue(o.encode(JSON.stringify({error:"Erro ao iniciar streaming com Vertex AI",message:e instanceof Error?e.message:String(e),type:"api_unavailable"}))),t.close()}})}}}class A extends Response{constructor(e,t){super(e,{...t,status:t?.status??200,headers:{"Content-Type":"text/plain; charset=utf-8","X-Content-Type-Options":"nosniff",...t?.headers}})}}class I extends Error{constructor(e,t,r=500,o=!1){super(e),this.name="AIAdapterError",this.type=t,this.statusCode=r,this.recoverable=o}static fromGeminiError(e){let t={[o.TIMEOUT]:{type:"timeout",statusCode:504},[o.API_UNAVAILABLE]:{type:"service_unavailable",statusCode:503},[o.INVALID_REQUEST]:{type:"bad_request",statusCode:400},[o.CONTENT_FILTERED]:{type:"content_filtered",statusCode:422},[o.RATE_LIMITED]:{type:"rate_limited",statusCode:429},[o.INVALID_RESPONSE_FORMAT]:{type:"bad_gateway",statusCode:502},[o.CONTEXT_LIMIT_EXCEEDED]:{type:"payload_too_large",statusCode:413},[o.TOKEN_LIMIT_EXCEEDED]:{type:"payload_too_large",statusCode:413},[o.RETRY_FAILED]:{type:"service_unavailable",statusCode:503},[o.UNKNOWN]:{type:"internal_error",statusCode:500}},{type:r,statusCode:a}=t[e.type]||t[o.UNKNOWN];return new I(e.message,r,a,e.recoverable)}}async function C(e,t={}){try{let r=e.filter(e=>"user"===e.role).pop();if(!r)throw new I("Nenhuma mensagem do usu\xe1rio encontrada","bad_request",400);let o=e.slice(0,-1).map(e=>({role:"user"===e.role?"user":"assistant",content:e.content})),a={};if(void 0!==t.temperature&&(a.temperature=t.temperature),void 0!==t.timeout&&(a.timeout=t.timeout),void 0!==t.userId&&(a.userId=t.userId),void 0!==t.systemPrompt&&(a.systemPrompt=t.systemPrompt),t.context){let e={};void 0!==t.context.activeSheet&&(e.activeSheet=t.context.activeSheet),void 0!==t.context.selection&&(e.selection=t.context.selection),void 0!==t.context.headers&&(e.headers=t.context.headers),void 0!==t.context.recentOperations&&(e.recentOperations=t.context.recentOperations),Object.keys(e).length>0&&(a.excelContext=e)}let s=E.getInstance(),n=await s.streamMessage(r.content,o,a),i=function(e,t){let r=new TextEncoder,o=new TextDecoder,a="";return new ReadableStream({async start(s){let n=e.getReader();try{let e=!1;for(;!e;){let t=await n.read();if(e=t.done,t.value){let e=o.decode(t.value);a+=e,s.enqueue(t.value)}}if(t?.onCompletion)try{let e=t.onCompletion(a);e!==a&&s.enqueue(r.encode("\n"+e))}catch(e){console.error("Erro no processamento final:",e)}t?.onFinal&&t.onFinal(),s.close()}catch(e){s.error(e)}}})}(n,{onFinal:()=>{},onCompletion:e=>{try{if(e.includes('"operations":')){let t=e.match(/\{[\s\S]*"operations"[\s\S]*\}/);if(t){let r=JSON.parse(t[0]);return JSON.stringify({operationsExecuted:!0,operationCount:r.operations?.length||0,dataUpdated:!0,completion:e})}}}catch(e){console.error("Erro ao analisar resposta:",e)}return e}});return new A(i)}catch(e){if(console.error("Erro no adaptador AI:",e),e instanceof f){let t=I.fromGeminiError(e);return new Response(JSON.stringify({error:t.message,type:t.type}),{status:t.statusCode})}return new Response(JSON.stringify({error:e instanceof Error?e.message:"Erro desconhecido",type:"internal_error"}),{status:500})}}var y=r(51552),v=r(63841);class _{constructor(){this.usageCache=new Map,this.CACHE_TTL=3e5,this.config={freeUserLimits:{requestsPerMinute:h.Vi.IS_PRODUCTION?10:50,requestsPerHour:h.Vi.IS_PRODUCTION?100:500,requestsPerDay:h.Vi.IS_PRODUCTION?500:2e3,maxTokensPerRequest:4e3},proUserLimits:{requestsPerMinute:h.Vi.IS_PRODUCTION?30:100,requestsPerHour:h.Vi.IS_PRODUCTION?500:1e3,requestsPerDay:h.Vi.IS_PRODUCTION?2e3:5e3,maxTokensPerRequest:8e3},operationLimits:{chat:{weight:1,cooldown:1e3},analysis:{weight:2,cooldown:2e3},generation:{weight:3,cooldown:3e3},translation:{weight:1.5,cooldown:1500}},blockDuration:{soft:3e5,hard:36e5}}}static getInstance(){return _.instance||(_.instance=new _),_.instance}async checkLimit(e,t="chat",r=1e3,o="free"){try{let a=await this.getUserMetrics(e);if(a.isBlocked&&a.blockUntil&&a.blockUntil>new Date)return{allowed:!1,remaining:{minute:0,hour:0,day:0},resetTime:{minute:0,hour:0,day:0},reason:`Usu\xe1rio bloqueado: ${a.blockReason}`,retryAfter:Math.ceil((a.blockUntil.getTime()-Date.now())/1e3)};let s="pro"===o?this.config.proUserLimits:this.config.freeUserLimits;if(r>s.maxTokensPerRequest)return{allowed:!1,remaining:{minute:0,hour:0,day:0},resetTime:{minute:0,hour:0,day:0},reason:`Requisi\xe7\xe3o excede limite de tokens (${r}/${s.maxTokensPerRequest})`};let n=this.config.operationLimits[t]?.weight||1,i=new Date,c=new Date(i.getFullYear(),i.getMonth(),i.getDate(),i.getHours(),i.getMinutes()),u=new Date(i.getFullYear(),i.getMonth(),i.getDate(),i.getHours()),l=new Date(i.getFullYear(),i.getMonth(),i.getDate()),d={minute:Math.max(0,s.requestsPerMinute-a.requestCount.minute),hour:Math.max(0,s.requestsPerHour-a.requestCount.hour),day:Math.max(0,s.requestsPerDay-a.requestCount.day)},p={minute:c.getTime()+6e4,hour:u.getTime()+36e5,day:l.getTime()+864e5};if(a.requestCount.minute+n>s.requestsPerMinute)return await this.recordViolation(e,"minute_limit_exceeded",t),{allowed:!1,remaining:d,resetTime:p,reason:"Limite por minuto excedido",retryAfter:Math.ceil((p.minute-i.getTime())/1e3)};if(a.requestCount.hour+n>s.requestsPerHour)return await this.recordViolation(e,"hour_limit_exceeded",t),{allowed:!1,remaining:d,resetTime:p,reason:"Limite por hora excedido",retryAfter:Math.ceil((p.hour-i.getTime())/1e3)};if(a.requestCount.day+n>s.requestsPerDay)return await this.recordViolation(e,"day_limit_exceeded",t),{allowed:!1,remaining:d,resetTime:p,reason:"Limite di\xe1rio excedido",retryAfter:Math.ceil((p.day-i.getTime())/1e3)};let m=this.config.operationLimits[t]?.cooldown||0,h=i.getTime()-a.lastRequest.getTime();if(h<m)return{allowed:!1,remaining:d,resetTime:p,reason:"Cooldown da opera\xe7\xe3o ativo",retryAfter:Math.ceil((m-h)/1e3)};return{allowed:!0,remaining:{minute:d.minute-n,hour:d.hour-n,day:d.day-n},resetTime:p}}catch(e){return g.kg.error("Erro ao verificar rate limit de IA:",e),{allowed:!0,remaining:{minute:0,hour:0,day:0},resetTime:{minute:0,hour:0,day:0},reason:"Erro interno - permitindo requisi\xe7\xe3o"}}}async recordUsage(e,t,r){try{let o=await this.getUserMetrics(e);o.requestCount.minute++,o.requestCount.hour++,o.requestCount.day++,o.tokenUsage.input+=r.input,o.tokenUsage.output+=r.output,o.tokenUsage.total+=r.input+r.output,o.operationCounts[t]=(o.operationCounts[t]||0)+1,o.lastRequest=new Date,this.usageCache.set(e,o),await this.persistMetrics(e,o)}catch(e){g.kg.error("Erro ao registrar uso de IA:",e)}}async getUserMetrics(e){let t=this.usageCache.get(e);if(t)return t;try{let t=new Date;t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getFullYear(),t.getMonth(),t.getDate();let[r,o,a]=[0,0,0],s={_sum:{inputTokens:0,outputTokens:0,totalTokens:0}},n={userId:e,requestCount:{minute:r,hour:o,day:a},tokenUsage:{input:s._sum?.inputTokens||0,output:s._sum?.outputTokens||0,total:s._sum?.totalTokens||0},operationCounts:{},lastRequest:new Date(0),isBlocked:!1};return this.usageCache.set(e,n),setTimeout(()=>this.usageCache.delete(e),this.CACHE_TTL),n}catch(t){return g.kg.error("Erro ao obter m\xe9tricas de usu\xe1rio:",t),{userId:e,requestCount:{minute:0,hour:0,day:0},tokenUsage:{input:0,output:0,total:0},operationCounts:{},lastRequest:new Date(0),isBlocked:!1}}}async persistMetrics(e,t){try{g.kg.debug("M\xe9tricas persistidas (temporariamente desabilitado)")}catch(e){g.kg.error("Erro ao persistir m\xe9tricas:",e)}}async recordViolation(e,t,r){try{g.kg.warn(`Rate limit violation: ${e} - ${t} - ${r}`)}catch(e){g.kg.error("Erro ao registrar viola\xe7\xe3o:",e)}}async getUsageStats(e){return this.getUserMetrics(e)}clearUserCache(e){this.usageCache.delete(e)}clearCache(){this.usageCache.clear()}}let T="force-dynamic",O="nodejs";async function S(e){try{let t=await (0,u.getServerSession)();if(!t?.user)return c.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let r=t.user.id,o=_.getInstance(),a=await o.checkLimit(r,"chat",1e3,"free");if(!a.allowed)return c.NextResponse.json({error:a.reason||"Limite de requisi\xe7\xf5es de IA excedido.",retryAfter:a.retryAfter||60,remaining:a.remaining,resetTime:a.resetTime},{status:429});let{messages:s,workbookId:n}=await e.json();if(!Array.isArray(s)||0===s.length)return c.NextResponse.json({error:"Formato de mensagem inv\xe1lido."},{status:400});let i=s[s.length-1];if(i?.content){let e=await (0,y.mB)(r,i.content);if(!e.allowed)return c.NextResponse.json({error:e.message||"Comando n\xe3o permitido para seu plano atual.",upgradeRequired:!0},{status:403})}let l=null;if(n){let e=await v.prisma.workbook.findFirst({where:{id:n,OR:[{userId:r},{shares:{some:{sharedWithUserId:r}}}]},include:{sheets:{take:1}}});e&&e.sheets&&e.sheets.length>0&&(l=e.sheets[0]?.data||null)}let d={workbookId:n||"",userId:r,context:{sheetData:l},temperature:.7};return await v.prisma.apiUsage.create({data:{userId:r,count:1,endpoint:"chat",billable:!0}}),C(s,d)}catch(e){return g.kg.error("[CHAT_API_ERROR]",e),c.NextResponse.json({error:"Ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.",details:void 0},{status:500})}}let R=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\chat\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:N,serverHooks:q}=R,k="/api/chat/route";function P(){return(0,i.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:N})}},43895:(e,t,r)=>{let o;r.d(t,{kg:()=>l});var a=r(99557),s=r.n(a);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],r={};return Object.keys(e).forEach(o=>{t.includes(o)||(r[o]=e[o])}),{normalizedError:e,extractedMetadata:r}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=u.production;o=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=s()({level:"info",formatters:{level:e=>({level:e})}})}let l={trace:(e,t)=>{o.trace(t||{},e)},debug:(e,t)=>{o.debug(t||{},e)},info:(e,t)=>{o.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:r}=i(t);o.warn(r,e)}else o.warn(c(t)||{},e)},error:(e,t,r)=>{let{normalizedError:a,extractedMetadata:s}=i(t),n={...r||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.error(n,e)},fatal:(e,t,r)=>{let{normalizedError:a,extractedMetadata:s}=i(t),n={...r||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.fatal(n,e)},createChild:e=>{let t=o.child(e);return{trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:o}=i(r);t.warn(o,e)}else t.warn(c(r)||{},e)},error:(e,r,o)=>{let{normalizedError:a,extractedMetadata:s}=i(r),n={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};t.error(n,e)},fatal:(e,r,o)=>{let{normalizedError:a,extractedMetadata:s}=i(r),n={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};t.fatal(n,e)}}},child:function(e){return this.createChild(e)}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,9557,7410,330,5609,1059,5431,2972,1552],()=>r(54005));module.exports=o})();