# ============================================================================
# 🔧 CONFIGURAÇÃO DE AMBIENTE - EXCEL COPILOT (PRODUÇÃO)
# ============================================================================
# ATENÇÃO: Este arquivo contém credenciais de produção
# Mantenha seguro e não commite no repositório
# ============================================================================

# ============================================================================
# CONFIGURAÇÕES BÁSICAS
# ============================================================================
NODE_ENV="production"
APP_NAME="Excel Copilot"
APP_VERSION="1.0.0"
APP_URL="https://excel-copilot-eight.vercel.app"

# ============================================================================
# AUTENTICAÇÃO - NEXTAUTH (OBRIGATÓRIAS)
# ============================================================================
# Gerar com: openssl rand -base64 32
AUTH_NEXTAUTH_SECRET="dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ"
AUTH_NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"
AUTH_SKIP_PROVIDERS="false"

# OAuth Providers
AUTH_GOOGLE_CLIENT_ID="217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com"
AUTH_GOOGLE_CLIENT_SECRET="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"
AUTH_GITHUB_CLIENT_ID="********************"
AUTH_GITHUB_CLIENT_SECRET="7c80b91c934dc9845a8ce7a362581d8ab45f2c3e"

# ============================================================================
# BANCO DE DADOS - SUPABASE (OBRIGATÓRIAS)
# ============================================================================
DB_DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"
DB_DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"

# Supabase Configuration
SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
NEXT_PUBLIC_SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0NTYxNCwiZXhwIjoyMDYyMTIxNjE0fQ.hHguPBu7OV6CJBSmwe3r7JwG1Ob__NWt-dWAnRsofP8"

# ============================================================================
# PAGAMENTOS - STRIPE (PRODUÇÃO)
# ============================================================================
STRIPE_SECRET_KEY="***********************************************************************************************************"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr"
STRIPE_WEBHOOK_SECRET="whsec_U2oN7gw62XH6DKOsGNbuqtbCAYLMDx8U"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_1RJeZYRrKLXtzZkMf1SS2CRR"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_1RJecORrKLXtzZkMy1RSRpMV"

# ============================================================================
# INTELIGÊNCIA ARTIFICIAL - VERTEX AI
# ============================================================================
# 🚀 PRODUÇÃO: IA habilitada com configuração unificada
AI_ENABLED="true"
AI_USE_MOCK="false"
AI_VERTEX_PROJECT_ID="excel-copilot"
AI_VERTEX_LOCATION="us-central1"
AI_VERTEX_MODEL="gemini-2.0-flash-001"

# ============================================================================
# CACHE E REDIS - CONFIGURAÇÃO COMPLETA
# ============================================================================
# Upstash Redis REST (para cache HTTP)
UPSTASH_REDIS_REST_URL="https://cunning-pup-26344.upstash.io"
UPSTASH_REDIS_REST_TOKEN="AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA"

# Redis nativo (para cache e filas)
REDIS_URL="redis://default:<EMAIL>:6379"
REDIS_HOST="cunning-pup-26344.upstash.io"
REDIS_PORT="6379"
REDIS_PASSWORD="AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA"

# Configurações de Filas (Bull/BullMQ) - PRODUÇÃO
QUEUE_REDIS_DB="1"  # Database 1 para filas (0 para cache)
QUEUE_CONCURRENCY="5"  # Máximo de jobs simultâneos em produção
QUEUE_MAX_ATTEMPTS="3"  # Tentativas máximas por job

# Configurações de cache otimizadas para produção
AI_CACHE_SIZE="200"
AI_CACHE_TTL="7200"
EXCEL_CACHE_SIZE="100"
EXCEL_CACHE_TTL="1800"
CACHE_DEFAULT_TTL="3600"

# ============================================================================
# SEGURANÇA E VALIDAÇÃO
# ============================================================================
DEV_FORCE_PRODUCTION="true"

# ============================================================================
# TESTES E2E - PLAYWRIGHT (PRODUÇÃO)
# ============================================================================
# Configurações para testes em produção
PLAYWRIGHT_BASE_URL="https://excel-copilot-eight.vercel.app"
# Credenciais de teste devem ser configuradas no CI/CD

# ============================================================================
# INTEGRAÇÕES MCP
# ============================================================================
# Vercel MCP
MCP_VERCEL_TOKEN="************************"
MCP_VERCEL_PROJECT_ID="prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg"
MCP_VERCEL_TEAM_ID="team_BLCIn3CF09teqBeBn8u0fLqp"

# Linear MCP
MCP_LINEAR_API_KEY="************************************************"

# GitHub MCP
MCP_GITHUB_TOKEN="****************************************"

# ============================================================================
# MONITORAMENTO E OBSERVABILIDADE
# ============================================================================
# Sentry
SENTRY_DSN="https://<EMAIL>/4509435346223104"
SENTRY_ORG="ngbprojectsentry"
SENTRY_PROJECT="excel-copilot"

# Vercel
VERCEL_BUILD_DATABASE_MIGRATION="false"
VERCEL_OIDC_TOKEN="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# ============================================================================
# INSTRUÇÕES DE CONFIGURAÇÃO
# ============================================================================
# ✅ ARQUIVO PREENCHIDO COM CREDENCIAIS REAIS DE PRODUÇÃO
# Todas as integrações estão configuradas e prontas para uso

# ============================================================================
# 🚨 PRÓXIMOS PASSOS IMPORTANTES
# ============================================================================
# ⚠️  SEGURANÇA: Este arquivo contém credenciais reais - NÃO commite no Git
# 🔧 VERCEL: Configure essas variáveis no painel do Vercel
# 🧪 TESTE: Valide se todas as integrações estão funcionando
# 📋 BACKUP: Mantenha uma cópia segura dessas credenciais

# ============================================================================
# 📋 COMANDO PARA CONFIGURAR NO VERCEL
# ============================================================================
# Use o Vercel CLI para configurar todas as variáveis de uma vez:
# vercel env add < .env.production
#
# Ou configure manualmente no painel: https://vercel.com/dashboard

# ============================================================================
# ✅ STATUS DAS INTEGRAÇÕES CONFIGURADAS
# ============================================================================
# 🚀 Vercel MCP: Token, Project ID e Team ID configurados
# 📋 Linear MCP: API Key configurada
# 🐙 GitHub MCP: Token configurado
# 🗄️ Supabase: URLs e chaves configuradas
# 💳 Stripe: Chaves LIVE de produção configuradas
# 🤖 Vertex AI: Projeto e modelo Gemini 2.0 configurados
# 📊 Sentry: DSN e projeto configurados
# 🔄 Redis: Cache + Filas Bull configurados
# 🔐 NextAuth: Google e GitHub OAuth configurados
# ⚡ Filas: Sistema assíncrono de IA configurado
# 🧪 Testes: Playwright E2E configurado para produção
# ============================================================================

# ============================================================================
# VARIÁVEIS COMPATÍVEIS COM NEXT.JS (MAPEAMENTO PARA SISTEMA LEGADO)
# ============================================================================
# Estas variáveis são necessárias para o build funcionar em produção
NEXTAUTH_SECRET="dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ"
NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"
DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"
CACHE_SECRET="d1bfc82ccb50ac0d97906034eb28ecf6607827897c4c49444f44c2026b2d319d"
