"use strict";(()=>{var e={};e.id=5761,e.ids=[5761],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},64443:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>E,patchFetch:()=>f,requestAsyncStorage:()=>v,routeModule:()=>_,serverHooks:()=>m,staticGenerationAsyncStorage:()=>T});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>g,dynamic:()=>h,runtime:()=>p});var o=t(49303),n=t(88716),a=t(60670),i=t(87070),c=t(52972),u=t(52202),l=t(43895);let h="force-dynamic",p="nodejs";async function d(e){try{l.kg.info("\uD83D\uDD0D Verifica\xe7\xe3o de sa\xfade da autentica\xe7\xe3o solicitada",{ip:e.ip,userAgent:e.headers.get("user-agent"),timestamp:new Date().toISOString()});let r=(0,u.hy)(),t=r.overall?200:503,s={status:r.overall?"healthy":"unhealthy",timestamp:r.timestamp,environment:c.Vi.NODE_ENV,checks:{environment:{status:r.environment.isValid?"pass":"fail",errors:r.environment.errors.length,warnings:r.environment.warnings.length,details:c.Vi.IS_PRODUCTION?void 0:{errors:r.environment.errors,warnings:r.environment.warnings}},providers:{google:{status:r.providers.google?"pass":"fail",configured:!!(process.env.AUTH_GOOGLE_CLIENT_ID&&process.env.AUTH_GOOGLE_CLIENT_SECRET)},github:{status:r.providers.github?"pass":"fail",configured:!!(process.env.AUTH_GITHUB_CLIENT_ID&&process.env.AUTH_GITHUB_CLIENT_SECRET)}},database:{status:process.env.DB_DATABASE_URL?"pass":"fail",configured:!!process.env.DB_DATABASE_URL},nextauth:{status:process.env.AUTH_NEXTAUTH_SECRET&&process.env.AUTH_NEXTAUTH_URL?"pass":"fail",secret_configured:!!process.env.AUTH_NEXTAUTH_SECRET,url_configured:!!process.env.AUTH_NEXTAUTH_URL}}};return r.overall?l.kg.info("✅ Health check de autentica\xe7\xe3o: SAUD\xc1VEL",{statusCode:t,checks:Object.keys(s.checks).length}):l.kg.warn("⚠️ Health check de autentica\xe7\xe3o: PROBLEMAS DETECTADOS",{statusCode:t,environmentErrors:r.environment.errors.length,environmentWarnings:r.environment.warnings.length,googleProvider:r.providers.google,githubProvider:r.providers.github}),i.NextResponse.json(s,{status:t})}catch(e){return l.kg.error("❌ Erro no health check de autentica\xe7\xe3o",{error:e instanceof Error?e.message:"Erro desconhecido",stack:e instanceof Error?e.stack:void 0}),i.NextResponse.json({status:"error",message:"Erro interno no health check",timestamp:new Date().toISOString()},{status:500})}}async function g(e){try{l.kg.info("\uD83D\uDD0D Verifica\xe7\xe3o detalhada de autentica\xe7\xe3o solicitada",{ip:e.ip,userAgent:e.headers.get("user-agent"),timestamp:new Date().toISOString()});let r=(0,u.hy)(),t={urls:{nextauth_url_valid:function(e){if(!e)return!1;try{return new URL(e),!0}catch{return!1}}(process.env.AUTH_NEXTAUTH_URL),nextauth_url_https:process.env.AUTH_NEXTAUTH_URL?.startsWith("https://")||c.Vi.IS_DEVELOPMENT},secrets:{nextauth_secret_length:(process.env.AUTH_NEXTAUTH_SECRET?.length||0)>=32,google_client_secret_length:(process.env.AUTH_GOOGLE_CLIENT_SECRET?.length||0)>=20,github_client_secret_length:(process.env.AUTH_GITHUB_CLIENT_SECRET?.length||0)>=30},format:{google_client_id_format:process.env.AUTH_GOOGLE_CLIENT_ID?.includes(".apps.googleusercontent.com")||!1,github_client_id_format:(process.env.AUTH_GITHUB_CLIENT_ID?.length||0)>=16}},s=r.overall&&Object.values(t.urls).every(Boolean)&&Object.values(t.secrets).every(Boolean)&&Object.values(t.format).every(Boolean),o={status:s?"healthy":"unhealthy",timestamp:new Date().toISOString(),environment:c.Vi.NODE_ENV,basic_checks:{environment:r.environment.isValid,google_provider:r.providers.google,github_provider:r.providers.github},detailed_checks:c.Vi.IS_PRODUCTION?void 0:t,recommendations:function(e,r){let t=[];return e.environment.isValid||t.push("Configure todas as vari\xe1veis de ambiente obrigat\xf3rias"),e.providers.google||t.push("Verifique as credenciais do Google OAuth"),e.providers.github||t.push("Verifique as credenciais do GitHub OAuth"),!r.urls.nextauth_url_https&&c.Vi.IS_PRODUCTION&&t.push("Use HTTPS para NEXTAUTH_URL em produ\xe7\xe3o"),r.secrets.nextauth_secret_length||t.push("NEXTAUTH_SECRET deve ter pelo menos 32 caracteres"),r.format.google_client_id_format||t.push("GOOGLE_CLIENT_ID deve terminar com .apps.googleusercontent.com"),t}(r,t)},n=s?200:503;return l.kg.info(s?"✅ Verifica\xe7\xe3o detalhada: APROVADA":"⚠️ Verifica\xe7\xe3o detalhada: PROBLEMAS",{statusCode:n,basicChecks:Object.values(o.basic_checks).filter(Boolean).length,recommendations:o.recommendations.length}),i.NextResponse.json(o,{status:n})}catch(e){return l.kg.error("❌ Erro na verifica\xe7\xe3o detalhada de autentica\xe7\xe3o",{error:e instanceof Error?e.message:"Erro desconhecido",stack:e instanceof Error?e.stack:void 0}),i.NextResponse.json({status:"error",message:"Erro interno na verifica\xe7\xe3o detalhada",timestamp:new Date().toISOString()},{status:500})}}let _=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/health/route",pathname:"/api/auth/health",filename:"route",bundlePath:"app/api/auth/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\health\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:T,serverHooks:m}=_,E="/api/auth/health/route";function f(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:T})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,9557,7410,2972,2202],()=>t(64443));module.exports=s})();