(()=>{var e={};e.id=3591,e.ids=[3591],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},39512:e=>{"use strict";e.exports=require("timers")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},39697:()=>{},62302:()=>{},30107:()=>{},47524:()=>{},12575:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>b,patchFetch:()=>w,requestAsyncStorage:()=>k,routeModule:()=>h,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>m,dynamic:()=>d});var i=t(49303),a=t(88716),s=t(60670),n=t(87070),c=t(90117),l=t(43895),u=t(63841);let d="force-dynamic";class p{constructor(){this.activeCollaborators=new Map,this.userSockets=new Map,this.socketUsers=new Map,this.workbookSockets=new Map}static getInstance(){return p.instance||(p.instance=new p),p.instance}addCollaborator(e,r){let t=this.activeCollaborators.get(e)||[],o=t.findIndex(e=>e.id===r.id);o>=0?t[o]=r:t.push(r),this.activeCollaborators.set(e,t),this.userSockets.set(r.id,r.socket),this.socketUsers.set(r.socket,r.id);let i=this.workbookSockets.get(e)||new Set;i.add(r.socket),this.workbookSockets.set(e,i)}removeCollaborator(e){let r=this.socketUsers.get(e);if(!r)return[];let t=[];return this.activeCollaborators.forEach((o,i)=>{let a=o.findIndex(e=>e.id===r);if(a>=0){o.splice(a,1),this.activeCollaborators.set(i,o),t.push(i);let r=this.workbookSockets.get(i);r&&(r.delete(e),0===r.size?this.workbookSockets.delete(i):this.workbookSockets.set(i,r))}}),this.userSockets.delete(r),this.socketUsers.delete(e),t}getCollaborators(e){return this.activeCollaborators.get(e)||[]}updateCollaboratorPosition(e,r){let t=this.socketUsers.get(e);if(!t)return null;let o=[];return this.activeCollaborators.forEach((e,i)=>{let a=e.find(e=>e.id===t);a&&(a.position=r,a.lastActive=new Date,a.status="active",o.push(i))}),{userId:t,workbookIds:o}}getWorkbookSockets(e){let r=this.workbookSockets.get(e);return r?Array.from(r):[]}}let f=null;async function m(e,r){try{if(!f){let e=r.socket.server;f=new c.xF(e,{path:"/api/socket",cors:{origin:process.env.AUTH_NEXTAUTH_URL||"*",methods:["GET","POST"],credentials:!0}});let t=p.getInstance();f.on("connection",async e=>{let{workbookId:r,userId:o,userName:i,userEmail:a}=e.handshake.auth;if(!r||!o){e.emit("error","Informa\xe7\xf5es de autentica\xe7\xe3o incompletas"),e.disconnect();return}try{if(!await u.prisma.workbook.findFirst({where:{id:r,OR:[{userId:o},{shares:{some:{sharedWithUserId:o}}}]}})){e.emit("error","Sem permiss\xe3o para acessar esta planilha"),e.disconnect();return}}catch(r){l.kg.error("Erro ao verificar permiss\xe3o de planilha:",r),e.emit("error","Erro ao verificar permiss\xe3o"),e.disconnect();return}l.kg.info(`Usu\xe1rio ${i} (${o}) conectado \xe0 planilha ${r}`),e.join(`workbook:${r}`);let s={id:o,name:i||"Usu\xe1rio",email:a||"",socket:e.id,status:"active",lastActive:new Date};t.addCollaborator(r,s),e.to(`workbook:${r}`).emit("collaborator_joined",{id:o,name:i,status:"active"});let n=t.getCollaborators(r);e.emit("collaborators",n),e.on("cursor_position",r=>{let o=t.updateCollaboratorPosition(e.id,r);if(o){let{userId:t,workbookIds:i}=o;i.forEach(o=>{e.to(`workbook:${o}`).emit("cursor_position",{userId:t,position:r,timestamp:Date.now()})})}}),e.on("cell_changed",async t=>{let a={...t,userId:o,userName:i,timestamp:Date.now()};e.to(`workbook:${r}`).emit("cell_changed",a)}),e.on("disconnect",()=>{t.removeCollaborator(e.id).forEach(r=>{e.to(`workbook:${r}`).emit("collaborator_left",o)}),l.kg.info(`Usu\xe1rio ${i} (${o}) desconectado`)})})}return new n.NextResponse("Socket.IO server is running",{status:200})}catch(e){return l.kg.error("Erro ao inicializar Socket.IO",e),new n.NextResponse("Failed to start Socket.IO server",{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/socket/route",pathname:"/api/socket",filename:"route",bundlePath:"app/api/socket/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\socket\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:k,staticGenerationAsyncStorage:g,serverHooks:v}=h,b="/api/socket/route";function w(){return(0,s.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},43183:(e,r)=>{"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */r.parse=function(e,r){if("string"!=typeof e)throw TypeError("argument str must be a string");var t={},i=e.length;if(i<2)return t;var a=r&&r.decode||u,s=0,n=0,d=0;do{if(-1===(n=e.indexOf("=",s)))break;if(-1===(d=e.indexOf(";",s)))d=i;else if(n>d){s=e.lastIndexOf(";",n-1)+1;continue}var p=c(e,s,n),f=l(e,n,p),m=e.slice(p,f);if(!o.call(t,m)){var h=c(e,n+1,d),k=l(e,d,h);34===e.charCodeAt(h)&&34===e.charCodeAt(k-1)&&(h++,k--);var g=e.slice(h,k);t[m]=function(e,r){try{return r(e)}catch(r){return e}}(g,a)}s=d+1}while(s<i);return t},r.serialize=function(e,r,o){var c=o&&o.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var l=c(r);if(!a.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(!o)return u;if(null!=o.maxAge){var d=Math.floor(o.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(o.domain){if(!s.test(o.domain))throw TypeError("option domain is invalid");u+="; Domain="+o.domain}if(o.path){if(!n.test(o.path))throw TypeError("option path is invalid");u+="; Path="+o.path}if(o.expires){var p=o.expires;if("[object Date]"!==t.call(p)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+p.toUTCString()}if(o.httpOnly&&(u+="; HttpOnly"),o.secure&&(u+="; Secure"),o.partitioned&&(u+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var t=Object.prototype.toString,o=Object.prototype.hasOwnProperty,i=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,a=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/;function c(e,r,t){do{var o=e.charCodeAt(r);if(32!==o&&9!==o)return r}while(++r<t);return t}function l(e,r,t){for(;r>t;){var o=e.charCodeAt(--r);if(32!==o&&9!==o)return r+1}return t}function u(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},43895:(e,r,t)=>{"use strict";let o;t.d(r,{kg:()=>u});var i=t(99557),a=t.n(i);function s(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function n(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:s(e),extractedMetadata:e}:{normalizedError:s(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let l={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=l.production;o=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=n(r);o.warn(t,e)}else o.warn(c(r)||{},e)},error:(e,r,t)=>{let{normalizedError:i,extractedMetadata:a}=n(r),s={...t||{},...a,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};o.error(s,e)},fatal:(e,r,t)=>{let{normalizedError:i,extractedMetadata:a}=n(r),s={...t||{},...a,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};o.fatal(s,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=n(t);r.warn(o,e)}else r.warn(c(t)||{},e)},error:(e,t,o)=>{let{normalizedError:i,extractedMetadata:a}=n(t),s={...o||{},...a,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};r.error(s,e)},fatal:(e,t,o)=>{let{normalizedError:i,extractedMetadata:a}=n(t),s={...o||{},...a,...i&&{error:{message:i.message,stack:i.stack,name:i.name}}};r.fatal(s,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{"use strict";t.d(r,{P:()=>c,prisma:()=>n});var o=t(53524);let i={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},s=[],n=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function l(){try{await n.$disconnect(),i.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){i.error("Erro ao desconectar do banco de dados",e)}}n.$on("query",e=>{a.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),a.averageQueryTime=s.reduce((e,r)=>e+r,0)/s.length),e.duration&&e.duration>500&&i.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),n.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),i.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,117],()=>t(12575));module.exports=o})();