"use strict";(()=>{var e={};e.id=9558,e.ids=[9558],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},41539:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>v,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>x,staticGenerationAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>i,HEAD:()=>p});var s=r(49303),n=r(88716),o=r(60670),u=r(87070),d=r(7843);async function i(e){try{if("true"===process.env.AUTH_SKIP_PROVIDERS)return u.NextResponse.json({message:"Mock statistics mode enabled",totalDeprecatedCalls:0,uniqueEndpoints:0,usageData:[],generateAt:new Date().toISOString()});let e=(0,d.xG)(),t=Object.entries(e).map(([e,t])=>({path:e,count:t,lastAccessed:new Date().toISOString()}));return u.NextResponse.json({totalDeprecatedCalls:Object.values(e).reduce((e,t)=>e+t,0),uniqueEndpoints:Object.keys(e).length,usageData:t,generateAt:new Date().toISOString()})}catch(e){return console.error("Erro ao obter estat\xedsticas de uso de APIs legadas:",e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function p(){return new Response(null,{status:200,headers:{"x-api-version":"2.0","x-deprecated":"true"}})}let c=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/deprecated-usage/route",pathname:"/api/deprecated-usage",filename:"route",bundlePath:"app/api/deprecated-usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\deprecated-usage\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:x}=c,m="/api/deprecated-usage/route";function v(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:g})}},7843:(e,t,r)=>{r.d(t,{sk:()=>s,xG:()=>n});let a={};function s(e,t,r){a[e]||(a[e]=0),a[e]++,console.warn(`[DEPRECATED API] Rota legada acessada: ${e}. Contador: ${a[e]}. User-Agent: ${t||"N\xe3o informado"}. Referer: ${r||"N\xe3o informado"}`)}function n(){return{...a}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972],()=>r(41539));module.exports=a})();