"use strict";(()=>{var e={};e.id=3794,e.ids=[3794],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},8044:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>T,requestAsyncStorage:()=>v,routeModule:()=>R,serverHooks:()=>f,staticGenerationAsyncStorage:()=>S});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>E,dynamic:()=>m,runtime:()=>d});var i=t(49303),o=t(88716),a=t(60670),u=t(52972),n=t(43895),c=t(69327),p=t(82840);let m="force-dynamic",d="nodejs";async function l(e){try{let e=process.env.STRIPE_SECRET_KEY,r=process.env.STRIPE_WEBHOOK_SECRET;if(!e)return p.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let t=new c.w({apiKey:e,...r&&{webhookSecret:r}}),s=await t.getBusinessStatus(),i={service:{name:"stripe",environment:u.Vi.IS_PRODUCTION?"production":"development",status:s.status,message:s.message,webhookConfigured:!!r},business:{revenue:{total:s.metrics.revenue.total,currency:s.metrics.revenue.currency,growth:s.metrics.revenue.growth,mrr:s.metrics.mrr,arpu:s.metrics.arpu},customers:{total:s.metrics.customers.total,new:s.metrics.customers.new,churn:s.metrics.customers.churn},subscriptions:{total:s.metrics.subscriptions.total,active:s.metrics.subscriptions.active,trialing:s.metrics.subscriptions.trialing,pastDue:s.metrics.subscriptions.pastDue,canceled:s.metrics.subscriptions.canceled},payments:{successful:s.metrics.payments.successful,failed:s.metrics.payments.failed,refunded:s.metrics.payments.refunded,successRate:s.metrics.payments.successRate}},recentActivity:s.recentActivity,alerts:s.alerts,timestamp:new Date().toISOString()};return n.kg.info("Status Stripe obtido com sucesso",{status:s.status,revenue:s.metrics.revenue.total,customers:s.metrics.customers.total}),p.R.success(i)}catch(e){if(n.kg.error("Erro ao obter status do Stripe",{error:e}),e instanceof Error)return p.R.error(`Erro ao conectar com Stripe: ${e.message}`,"STRIPE_CONNECTION_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function E(e){try{let e=process.env.STRIPE_SECRET_KEY,r=process.env.STRIPE_WEBHOOK_SECRET;if(!e)return p.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let t=new c.w({apiKey:e,...r&&{webhookSecret:r}}),s=await t.getRevenueAnalysis("30d"),i=await t.getExcelCopilotMetrics(),o={revenue:s,excelCopilot:i,timestamp:new Date().toISOString()};return p.R.success(o)}catch(e){return n.kg.error("Erro na verifica\xe7\xe3o for\xe7ada do Stripe",{error:e}),p.R.error("Erro ao verificar status","STRIPE_STATUS_ERROR",500)}}let R=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/stripe/status/route",pathname:"/api/stripe/status",filename:"route",bundlePath:"app/api/stripe/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\status\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:S,serverHooks:f}=R,x="/api/stripe/status/route";function T(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:S})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,9557,7410,1059,2972,5767],()=>t(8044));module.exports=s})();