"use strict";(()=>{var e={};e.id=2276,e.ids=[2276],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},92003:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>_,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>R,POST:()=>E,dynamic:()=>c,runtime:()=>d});var s=t(49303),n=t(88716),i=t(60670),a=t(43895),l=t(89314),u=t(82840);let c="force-dynamic",d="nodejs";async function R(e){try{let r=process.env.MCP_VERCEL_TOKEN,t=process.env.MCP_VERCEL_PROJECT_ID,o=process.env.MCP_VERCEL_TEAM_ID;if(!r)return u.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let{searchParams:s}=new URL(e.url),n=s.get("level"),i=s.get("source"),c=parseInt(s.get("limit")||"50"),d=s.get("search");if(n&&!["info","warn","error"].includes(n))return u.R.error("Par\xe2metro level inv\xe1lido. Use: info, warn, error","INVALID_PARAMETER",400);if(i&&!["build","static","lambda","edge"].includes(i))return u.R.error("Par\xe2metro source inv\xe1lido. Use: build, static, lambda, edge","INVALID_PARAMETER",400);if(c<1||c>200)return u.R.error("Par\xe2metro limit deve estar entre 1 e 200","INVALID_PARAMETER",400);let R=new l.n(r,o,t),E=(await R.getFilteredLogs({...n&&{level:n},...i&&{source:i},limit:c,...d&&{search:d}})).map(e=>({timestamp:new Date(e.timestamp).toISOString(),level:e.level,source:e.source,message:e.message,deploymentId:e.deploymentId,requestId:e.requestId,region:e.region})),p={logs:E,total:E.length,filters:{level:n||"all",source:i||"all",search:d||null,limit:c},timestamp:new Date().toISOString()};return a.kg.info("Logs Vercel obtidos com sucesso",{count:E.length,level:n,source:i}),u.R.success(p)}catch(e){if(a.kg.error("Erro ao obter logs do Vercel",{error:e}),e instanceof Error)return u.R.error(`Erro ao conectar com Vercel: ${e.message}`,"VERCEL_API_ERROR",500);return u.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function E(e){try{let r;let t=process.env.MCP_VERCEL_TOKEN,o=process.env.MCP_VERCEL_PROJECT_ID,s=process.env.MCP_VERCEL_TEAM_ID;if(!t)return u.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let{level:n,source:i,limit:a=50,search:c,deploymentId:d,since:R,until:E}=await e.json();if(n&&!["info","warn","error"].includes(n))return u.R.error("Par\xe2metro level inv\xe1lido","INVALID_PARAMETER",400);if(i&&!["build","static","lambda","edge"].includes(i))return u.R.error("Par\xe2metro source inv\xe1lido","INVALID_PARAMETER",400);if(a<1||a>500)return u.R.error("Par\xe2metro limit deve estar entre 1 e 500","INVALID_PARAMETER",400);let p=new l.n(t,s,o);if(d){let e=p.client;if(r=await e.getDeploymentLogs(d,{...R&&{since:new Date(R).getTime()},...E&&{until:new Date(E).getTime()},limit:a}),n&&(r=r.filter(e=>e.level===n)),i&&(r=r.filter(e=>e.source===i)),c){let e=c.toLowerCase();r=r.filter(r=>r.message.toLowerCase().includes(e))}}else r=await p.getFilteredLogs({level:n,source:i,limit:a,search:c});let m=r.map(e=>({timestamp:new Date(e.timestamp).toISOString(),level:e.level,source:e.source,message:e.message,deploymentId:e.deploymentId,requestId:e.requestId,region:e.region})),g={logs:m,total:m.length,filters:{level:n||"all",source:i||"all",search:c||null,deploymentId:d||null,limit:a,since:R||null,until:E||null},timestamp:new Date().toISOString()};return u.R.success(g)}catch(e){return a.kg.error("Erro na busca avan\xe7ada de logs do Vercel",{error:e}),u.R.error("Erro ao buscar logs","VERCEL_LOGS_ERROR",500)}}let p=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/vercel/logs/route",pathname:"/api/vercel/logs",filename:"route",bundlePath:"app/api/vercel/logs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\logs\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:I}=p,v="/api/vercel/logs/route";function _(){return(0,i.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},82840:(e,r,t)=>{t.d(r,{R:()=>n});var o=t(87070),s=t(43895);let n={success(e,r,t=200){let s={data:e,...r&&{meta:r}};return o.NextResponse.json(s,{status:t})},error(e,r="INTERNAL_ERROR",t=500,n){let i={code:r,message:e,timestamp:new Date().toISOString(),...void 0!==n&&{details:n}};return s.kg.error(`API Error [${r}]: ${e}`,{details:n}),o.NextResponse.json(i,{status:t})},unauthorized(e="N\xe3o autorizado",r){return this.error(e,"UNAUTHORIZED",401,r)},badRequest(e,r){return this.error(e,"BAD_REQUEST",400,r)},notFound(e="Recurso n\xe3o encontrado",r){return this.error(e,"NOT_FOUND",404,r)},forbidden(e="Acesso negado",r){return this.error(e,"FORBIDDEN",403,r)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r){let t={};return r&&(t["Retry-After"]=r.toString()),o.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:t})}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,9314],()=>t(92003));module.exports=o})();