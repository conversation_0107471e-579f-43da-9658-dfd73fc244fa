"use strict";(()=>{var e={};e.id=4260,e.ids=[4260],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2025:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{DELETE:()=>h,GET:()=>p,POST:()=>c,PUT:()=>l});var s=r(49303),o=r(88716),n=r(60670),i=r(87070),u=r(60756);async function p(e){try{let t=new URL(e.url),r="false"!==t.searchParams.get("details"),a=await (0,u.checkService)("ai"),s=(0,u.healthStatusToHttpCode)(a.status),o=(0,u.formatHealthResponse)(a,r);return i.NextResponse.json(o,{status:s,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(t){let e=t instanceof Error?t.message:"Unknown error";return i.NextResponse.json({status:"unhealthy",service:"ai",timestamp:new Date().toISOString(),responseTime:0,error:e},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function c(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function l(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function h(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/ai/route",pathname:"/api/health/ai",filename:"route",bundlePath:"app/api/health/ai/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\ai\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:v}=d,f="/api/health/ai/route";function g(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,756],()=>r(2025));module.exports=a})();