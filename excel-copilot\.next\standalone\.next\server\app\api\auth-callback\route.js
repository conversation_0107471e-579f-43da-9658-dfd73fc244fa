"use strict";(()=>{var e={};e.id=621,e.ids=[621],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9934:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>k,patchFetch:()=>m,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var a={};t.r(a),t.d(a,{GET:()=>s,dynamic:()=>p});var o=t(49303),n=t(88716),c=t(60670),i=t(87070);let p="force-dynamic";async function s(e){try{let{searchParams:r}=new URL(e.url),t={};r.forEach((e,r)=>{t[r]=e});let a=r.get("error"),o=r.get("error_description");if(a)return i.NextResponse.redirect(new URL(`/auth/signin?error=${a}&error_description=${encodeURIComponent(o||"")}&note=callback_path_deprecated`,e.url));return i.NextResponse.redirect(new URL("/dashboard?callback_path=deprecated",e.url))}catch{return i.NextResponse.redirect(new URL("/auth/signin?error=callback_error&note=callback_path_deprecated",e.url))}}let u=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth-callback/route",pathname:"/api/auth-callback",filename:"route",bundlePath:"app/api/auth-callback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth-callback\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:h}=u,k="/api/auth-callback/route";function m(){return(0,c.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972],()=>t(9934));module.exports=a})();