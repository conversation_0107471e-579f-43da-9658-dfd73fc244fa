"use strict";(()=>{var e={};e.id=1419,e.ids=[1419],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30409:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>m,dynamic:()=>d});var o=r(49303),a=r(88716),n=r(60670),i=r(87070),u=r(52972),p=r(81222);let d="force-dynamic";async function m(e){try{if(u.Vi.IS_PRODUCTION)return i.NextResponse.json({error:"N\xe3o dispon\xedvel em produ\xe7\xe3o",message:"Este endpoint s\xf3 est\xe1 dispon\xedvel em desenvolvimento"},{status:403});let t=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||e.ip||"::1",r=e.headers.get("user-agent")||void 0;if((0,p.lv)(t,r))return i.NextResponse.json({success:!0,message:"Rate limit resetado com sucesso",ip:t,timestamp:new Date().toISOString()});return i.NextResponse.json({success:!1,message:"Nenhum rate limit encontrado para este IP",ip:t,timestamp:new Date().toISOString()})}catch(e){return console.error("Erro ao resetar rate limit:",e),i.NextResponse.json({error:"Erro interno",message:"Erro ao resetar rate limit",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}async function l(e){try{if(u.Vi.IS_PRODUCTION)return i.NextResponse.json({error:"N\xe3o dispon\xedvel em produ\xe7\xe3o"},{status:403});let t=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||e.ip||"::1";return i.NextResponse.json({ip:t,message:"Use POST para resetar o rate limit",timestamp:new Date().toISOString()})}catch(e){return i.NextResponse.json({error:"Erro interno",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/reset-rate-limit/route",pathname:"/api/auth/reset-rate-limit",filename:"route",bundlePath:"app/api/auth/reset-rate-limit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\reset-rate-limit\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:f}=c,x="/api/auth/reset-rate-limit/route";function v(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},81222:(e,t,r)=>{r.d(t,{j2:()=>n,lv:()=>a}),r(87070);let s=new Map,o={maxAttempts:5};function a(e,t){let r=Buffer.from(`${e}-${t||"unknown"}`).toString("base64").slice(0,16),o=`oauth_limit:${r}`;return s.delete(o)}function n(){let e=Date.now(),t=0,r=0;for(let a of s.values())e<=a.resetTime&&(r++,a.count>o.maxAttempts&&t++);return{totalEntries:s.size,blockedIPs:t,activeEntries:r}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,7410,2972],()=>r(30409));module.exports=s})();