(()=>{var e={};e.id=4668,e.ids=[4668],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},85339:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(82577),s(65675),s(12523);var t=s(23191),o=s(88716),a=s(37922),n=s.n(a),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d=["",{children:["examples",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82577)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx"],p="/examples/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/examples/page",pathname:"/examples",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},17594:(e,r,s)=>{Promise.resolve().then(s.bind(s,75982))},38443:(e,r,s)=>{"use strict";s.d(r,{C:()=>i});var t=s(10326),o=s(79360);s(17577);var a=s(51223);let n=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...s}){return t.jsx("div",{className:(0,a.cn)(n({variant:r}),e),...s})}},29752:(e,r,s)=>{"use strict";s.d(r,{Ol:()=>d,SZ:()=>p,Zb:()=>l,aY:()=>x,eW:()=>u,ll:()=>c});var t=s(10326),o=s(31722),a=s(17577),n=s(45365),i=s(51223);let l=(0,a.forwardRef)(({className:e,children:r,hoverable:s=!1,variant:a="default",noPadding:l=!1,animated:d=!1,...c},p)=>{let x=(0,i.cn)("rounded-xl border shadow-sm",{"p-6":!l,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":s&&!d,"border-border bg-card":"default"===a,"border-border/50 bg-transparent":"outline"===a,"bg-card/90 backdrop-blur-md border-border/50":"glass"===a,"bg-gradient-primary text-primary-foreground border-none":"gradient"===a},e);return d?t.jsx(o.E.div,{ref:p,className:x,...(0,n.Ph)("card"),whileHover:s?n.q.hover:void 0,whileTap:s?n.q.tap:void 0,...c,children:r}):t.jsx("div",{ref:p,className:x,...c,children:r})});l.displayName="Card";let d=(0,a.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("mb-4 flex flex-col space-y-1.5",e),...r}));d.displayName="CardHeader";let c=(0,a.forwardRef)(({className:e,...r},s)=>t.jsx("h3",{ref:s,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));c.displayName="CardTitle";let p=(0,a.forwardRef)(({className:e,...r},s)=>t.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));p.displayName="CardDescription";let x=(0,a.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("card-content",e),...r}));x.displayName="CardContent";let u=(0,a.forwardRef)(({className:e,...r},s)=>t.jsx("div",{ref:s,className:(0,i.cn)("flex items-center pt-4 mt-auto",e),...r}));u.displayName="CardFooter"},82577:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(19510),o=s(68570);let a=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx`),{__esModule:n,$$typeof:i}=a;a.default,(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx#WorkbookTemplates`);let l=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx#WorkbookTemplatesServer`);function d(){return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-10",children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight mb-4",children:"Modelos e Exemplos"}),t.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Comece rapidamente com nossos modelos e veja exemplos de comandos poderosos."})]}),(0,t.jsxs)("section",{className:"py-8",children:[t.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Modelos Prontos"}),t.jsx(l,{})]}),(0,t.jsxs)("section",{className:"py-8",children:[t.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Exemplos de Comandos"}),(0,t.jsxs)("div",{className:"bg-card p-6 rounded-lg border shadow-sm",children:[t.jsx("p",{className:"mb-4",children:"Confira exemplos de comandos que voc\xea pode usar com nossa IA:"}),(0,t.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[t.jsx("li",{children:"Crie uma planilha de controle financeiro com categorias de gastos"}),t.jsx("li",{children:"Gere um gr\xe1fico de barras com os dados das colunas A e B"}),t.jsx("li",{children:"Some os valores da coluna Vendas"}),t.jsx("li",{children:"Filtre os dados onde Pre\xe7o \xe9 maior que 100"}),t.jsx("li",{children:"Converta a planilha atual para formato de tabela"}),t.jsx("li",{children:"Crie uma tabela din\xe2mica agrupando vendas por regi\xe3o"}),t.jsx("li",{children:"Fa\xe7a uma an\xe1lise de correla\xe7\xe3o entre pre\xe7o e demanda"})]})]})]})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,9557,7410,86,7915,5999,2972,4433,6841,611],()=>s(85339));module.exports=t})();