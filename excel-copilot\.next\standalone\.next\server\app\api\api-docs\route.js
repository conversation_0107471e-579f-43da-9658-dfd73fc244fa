"use strict";(()=>{var e={};e.id=5139,e.ids=[5139],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},41507:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>f,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>A,staticGenerationAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>l,dynamic:()=>p});var s=t(49303),o=t(88716),i=t(60670),n=t(21270),u=t(99747),d=t(82840);let p="force-dynamic",c={info:{title:"Excel Copilot API",description:"API para manipula\xe7\xe3o de planilhas Excel atrav\xe9s de linguagem natural",version:"1.0.0",contact:{email:"<EMAIL>"},license:{name:"Propriet\xe1rio"}},servers:[{url:"https://excel-copilot-eight.vercel.app",description:"Servidor de Produ\xe7\xe3o"},{url:"http://localhost:3000",description:"Servidor Local de Desenvolvimento"}],tags:[{name:"Auth",description:"Opera\xe7\xf5es de autentica\xe7\xe3o e autoriza\xe7\xe3o"},{name:"Workbooks",description:"Opera\xe7\xf5es com planilhas"},{name:"Sheets",description:"Opera\xe7\xf5es com folhas dentro de planilhas"},{name:"AI",description:"Opera\xe7\xf5es com Intelig\xeancia Artificial para manipula\xe7\xe3o de dados"},{name:"Excel",description:"Opera\xe7\xf5es espec\xedficas do Excel"},{name:"Usu\xe1rios",description:"Opera\xe7\xf5es de gerenciamento de usu\xe1rios"},{name:"Admin",description:"Opera\xe7\xf5es administrativas (requer permiss\xe3o)"}],paths:{"/api/auth/login":{post:{tags:["Auth"],summary:"Login de usu\xe1rio",description:"Autentica um usu\xe1rio e retorna token de acesso",requiresAuth:!1}},"/api/auth/register":{post:{tags:["Auth"],summary:"Registro de usu\xe1rio",description:"Registra um novo usu\xe1rio no sistema",requiresAuth:!1}},"/api/workbooks":{get:{tags:["Workbooks"],summary:"Listar planilhas",description:"Lista todas as planilhas do usu\xe1rio",requiresAuth:!0},post:{tags:["Workbooks"],summary:"Criar planilha",description:"Cria uma nova planilha",requiresAuth:!0},patch:{tags:["Workbooks"],summary:"Atualizar planilha",description:"Atualiza informa\xe7\xf5es de uma planilha existente",requiresAuth:!0},delete:{tags:["Workbooks"],summary:"Excluir planilha",description:"Exclui uma planilha existente",requiresAuth:!0}},"/api/workbooks/{id}":{get:{tags:["Workbooks"],summary:"Obter planilha",description:"Obt\xe9m detalhes de uma planilha espec\xedfica",requiresAuth:!0}},"/api/sheets":{post:{tags:["Sheets"],summary:"Criar folha",description:"Cria uma nova folha em uma planilha existente",requiresAuth:!0},patch:{tags:["Sheets"],summary:"Atualizar folha",description:"Atualiza dados de uma folha existente",requiresAuth:!0},delete:{tags:["Sheets"],summary:"Excluir folha",description:"Exclui uma folha existente",requiresAuth:!0}},"/api/chat":{post:{tags:["AI"],summary:"Chat com IA",description:"Envia mensagem para processamento por IA",requiresAuth:!0}},"/api/ai/status":{get:{tags:["AI"],summary:"Status da IA",description:"Obt\xe9m o status dos servi\xe7os de IA",requiresAuth:!1}},"/api/excel/analyze":{post:{tags:["Excel"],summary:"Analisar dados",description:"Analisa dados para obter insights",requiresAuth:!0}},"/api/excel/export":{post:{tags:["Excel"],summary:"Exportar planilha",description:"Exporta planilha em diversos formatos",requiresAuth:!0}},"/api/excel/import":{post:{tags:["Excel"],summary:"Importar planilha",description:"Importa planilha de diferentes formatos",requiresAuth:!0}},"/api/user/profile":{get:{tags:["Usu\xe1rios"],summary:"Perfil de usu\xe1rio",description:"Obt\xe9m perfil do usu\xe1rio atual",requiresAuth:!0},patch:{tags:["Usu\xe1rios"],summary:"Atualizar perfil",description:"Atualiza informa\xe7\xf5es do perfil do usu\xe1rio",requiresAuth:!0}},"/api/user/api-usage":{get:{tags:["Usu\xe1rios"],summary:"Uso da API",description:"Obt\xe9m estat\xedsticas de uso da API pelo usu\xe1rio",requiresAuth:!0}},"/api/webhooks/stripe":{post:{tags:["Admin"],summary:"Webhook Stripe",description:"Recebe eventos de pagamento do Stripe",requiresAuth:!1}},"/api/metrics":{get:{tags:["Admin"],summary:"M\xe9tricas",description:"Obt\xe9m m\xe9tricas de desempenho da aplica\xe7\xe3o",requiresAuth:!0,adminOnly:!0}},"/api/health/db":{get:{tags:["Admin"],summary:"Status do Banco",description:"Verifica a sa\xfade do banco de dados",requiresAuth:!1}}}},l=(0,u.x)([n.q],async(e,r)=>{try{let e={...c.paths};return r.isAuthenticated||(e=Object.entries(e).reduce((e,[r,t])=>(Object.values(t).some(e=>e&&"object"==typeof e&&"adminOnly"in e&&e.adminOnly)||(e[r]=t),e),{})),d.R.success({...c,paths:e})}catch(e){if(e instanceof Error)return d.R.error(e.message);return d.R.error("Erro ao gerar documenta\xe7\xe3o da API")}}),m=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/api-docs/route",pathname:"/api/api-docs",filename:"route",bundlePath:"app/api/api-docs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\api-docs\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:A}=m,x="/api/api-docs/route";function f(){return(0,i.patchFetch)({serverHooks:A,staticGenerationAsyncStorage:g})}},21270:(e,r,t)=>{t.d(r,{l:()=>u,q:()=>d});var a=t(45609),s=t(43895),o=t(81628),i=t(63841),n=t(82840);async function u(e,r,t){var u;let d=Date.now(),p=function(e){let r=e.headers,t=r instanceof Headers?r.get("x-forwarded-for"):r?.["x-forwarded-for"],a=r instanceof Headers?r.get("x-real-ip"):r?.["x-real-ip"],s=e.connection?.remoteAddress||e.socket?.remoteAddress,o=Array.isArray(t)?t[0]:t,i=Array.isArray(a)?a[0]:a,n=o?.split(",")[0]||i||s||"unknown";return/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(n)||/^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(n)?n:"unknown"}(e),c=(u=e.headers.get("user-agent")||void 0)&&u.substring(0,200).replace(/[<>"'&]/g,"").replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g,"[IP]").replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,"[EMAIL]")||"Unknown";try{let r=await (0,a.getServerSession)(o.L);if(!r?.user)return s.kg.warn("\uD83D\uDEAB Acesso n\xe3o autorizado a rota protegida",{path:e.nextUrl.pathname,method:e.method,ip:p,userAgent:c,timestamp:new Date().toISOString(),duration:Date.now()-d}),n.R.unauthorized("Usu\xe1rio n\xe3o autenticado");t.session=r,t.user=r.user;let u=r.user.id;if(u){t.userId=u;try{let r=await i.prisma.user.findUnique({where:{id:u}});if(r&&!0===r.isBanned){s.kg.warn("Tentativa de acesso por usu\xe1rio banido",{userId:u,path:e.nextUrl.pathname,banReason:r.banReason,banDate:r.banDate});try{await i.prisma.session.deleteMany({where:{userId:u}})}catch(e){s.kg.error("Erro ao revogar sess\xe3o de usu\xe1rio banido",e)}return n.R.forbidden(`Conta suspensa: ${r.banReason||"Viola\xe7\xe3o de termos de uso"}`)}let t={lastIpAddress:p,lastLoginAt:new Date,loginCount:{increment:1}};null!==r&&(t.userAgent=c),await i.prisma.user.update({where:{id:u},data:t}),s.kg.debug("\uD83D\uDCCA Dados de auditoria atualizados",{userId:u,ip:p,userAgent:c.substring(0,50)+"...",path:e.nextUrl.pathname})}catch(e){s.kg.error("Erro ao verificar dados do usu\xe1rio",e)}}s.kg.debug("✅ Usu\xe1rio autenticado com sucesso",{userId:t.userId,path:e.nextUrl.pathname,duration:Date.now()-d})}catch(r){return s.kg.error("❌ Erro cr\xedtico ao verificar autentica\xe7\xe3o",{error:r instanceof Error?r.message:"Erro desconhecido",stack:r instanceof Error?r.stack:void 0,path:e.nextUrl.pathname,method:e.method,ip:p,duration:Date.now()-d}),n.R.error("Erro ao verificar autentica\xe7\xe3o","AUTH_ERROR",500)}}async function d(e,r,t){try{let r=await (0,a.getServerSession)(o.L);if(r?.user){t.session=r,t.user=r.user;let a=r.user.id;if(a){t.userId=a;try{let r=await i.prisma.user.findUnique({where:{id:a}});r&&!0===r.isBanned?(t.isBanned=!0,s.kg.warn("Usu\xe1rio banido acessando rota opcional",{userId:a,path:e.nextUrl.pathname})):t.isBanned=!1}catch(e){s.kg.error("Erro ao verificar banimento do usu\xe1rio",e),t.isBanned=!1}}t.isAuthenticated=!0}else t.isAuthenticated=!1,t.isBanned=!1}catch(e){s.kg.error("Erro ao verificar autentica\xe7\xe3o opcional",e),t.isAuthenticated=!1,t.isBanned=!1}}},99747:(e,r,t)=>{t.d(r,{x:()=>o});var a=t(87070),s=t(43895);function o(e,r){let t=function(...e){return async(r,t=new a.NextResponse,o={})=>{try{for(let s of e){let e=await s(r,t,o);if(e instanceof a.NextResponse)return e}return{res:t,context:o}}catch(e){return s.kg.error("Erro na cadeia de middleware",e),a.NextResponse.json({code:"MIDDLEWARE_ERROR",message:"Erro interno no servidor",timestamp:new Date().toISOString()},{status:500})}}}(...e);return async e=>{let o=new a.NextResponse,i={},n=await t(e,o,i);if(n instanceof a.NextResponse)return n;try{let t=n&&"object"==typeof n&&"context"in n?{...i,...n.context}:i;return await r(e,t)}catch(e){return s.kg.error("Erro no handler de API",e),a.NextResponse.json({code:"HANDLER_ERROR",message:e instanceof Error?e.message:"Erro interno no servidor",timestamp:new Date().toISOString()},{status:500})}}}},82840:(e,r,t)=>{t.d(r,{R:()=>o});var a=t(87070),s=t(43895);let o={success(e,r,t=200){let s={data:e,...r&&{meta:r}};return a.NextResponse.json(s,{status:t})},error(e,r="INTERNAL_ERROR",t=500,o){let i={code:r,message:e,timestamp:new Date().toISOString(),...void 0!==o&&{details:o}};return s.kg.error(`API Error [${r}]: ${e}`,{details:o}),a.NextResponse.json(i,{status:t})},unauthorized(e="N\xe3o autorizado",r){return this.error(e,"UNAUTHORIZED",401,r)},badRequest(e,r){return this.error(e,"BAD_REQUEST",400,r)},notFound(e="Recurso n\xe3o encontrado",r){return this.error(e,"NOT_FOUND",404,r)},forbidden(e="Acesso negado",r){return this.error(e,"FORBIDDEN",403,r)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r){let t={};return r&&(t["Retry-After"]=r.toString()),a.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:t})}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(41507));module.exports=a})();