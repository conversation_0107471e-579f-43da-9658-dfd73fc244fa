"use strict";(()=>{var e={};e.id=1607,e.ids=[1607],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},13588:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>w,patchFetch:()=>y,requestAsyncStorage:()=>h,routeModule:()=>v,serverHooks:()=>x,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{POST:()=>f,dynamic:()=>p,runtime:()=>m});var n=t(49303),a=t(88716),s=t(60670),i=t(87070),c=t(45609),l=t(43895),u=t(46029),d=t(63841);let p="force-dynamic",m="nodejs";async function f(e){try{let r;if(!u.Ag)return i.NextResponse.json({error:"Stripe n\xe3o est\xe1 configurado."},{status:503});let t=await (0,c.getServerSession)();if(!t?.user)return i.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let o=t.user.id,{plan:n,successUrl:a,cancelUrl:s}=await e.json();if(!n||!a||!s)return i.NextResponse.json({error:"Dados incompletos. Por favor, forne\xe7a plano e URLs de redirecionamento."},{status:400});let l=await d.prisma.user.findUnique({where:{id:o}});if(!l)return i.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado."},{status:404});let p=await d.prisma.subscription.findFirst({where:{userId:o},select:{stripeCustomerId:!0}});if(p?.stripeCustomerId)r=p.stripeCustomerId;else{let e=t.user.email||"",n=t.user.name||"";r=(await u.Ag.customers.create({email:e,name:n,metadata:{userId:o}})).id}let m=(0,u.Al)(n),f=await u.Ag.checkout.sessions.create({customer:r,line_items:[{price:m,quantity:1}],mode:"subscription",success_url:a,cancel_url:s,allow_promotion_codes:!0,billing_address_collection:"auto",metadata:{userId:l.id,plan:n},payment_method_types:["card"],locale:"pt-BR",subscription_data:{metadata:{userId:l.id,plan:n}}});return i.NextResponse.json({url:f.url})}catch(e){return l.kg.error("[CHECKOUT_ERROR]",e),i.NextResponse.json({error:"Erro ao processar o checkout. Por favor, tente novamente.",details:void 0},{status:500})}}let v=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/checkout/route",pathname:"/api/checkout",filename:"route",bundlePath:"app/api/checkout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:x}=v,w="/api/checkout/route";function y(){return(0,s.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:g})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>u});var n=t(99557),a=t.n(n);function s(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:s(e),extractedMetadata:e}:{normalizedError:s(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let l={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=l.production;o=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);o.warn(t,e)}else o.warn(c(r)||{},e)},error:(e,r,t)=>{let{normalizedError:n,extractedMetadata:a}=i(r),s={...t||{},...a,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};o.error(s,e)},fatal:(e,r,t)=>{let{normalizedError:n,extractedMetadata:a}=i(r),s={...t||{},...a,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};o.fatal(s,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=i(t);r.warn(o,e)}else r.warn(c(t)||{},e)},error:(e,t,o)=>{let{normalizedError:n,extractedMetadata:a}=i(t),s={...o||{},...a,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.error(s,e)},fatal:(e,t,o)=>{let{normalizedError:n,extractedMetadata:a}=i(t),s={...o||{},...a,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.fatal(s,e)}}},child:function(e){return this.createChild(e)}}},46029:(e,r,t)=>{t.d(r,{Ag:()=>c,Al:()=>l,DI:()=>u,Xf:()=>a,cb:()=>s}),t(30468);var o=t(31059);let n={PRO_MONTHLY:"price_1RWHbARrKLXtzZkME498Zuab",PRO_ANNUAL:"price_1RWHckRrKLXtzZkMLLn1vFvh"},a={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},s={[a.FREE]:50,[a.PRO_MONTHLY]:500,[a.PRO_ANNUAL]:1e3},i=process.env.STRIPE_SECRET_KEY||"",c=i?new o.Z(i,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function l(e){switch(e){case a.PRO_MONTHLY:return n.PRO_MONTHLY;case a.PRO_ANNUAL:return n.PRO_ANNUAL;default:return n.PRO_MONTHLY}}function u(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>c,prisma:()=>i});var o=t(53524);let n={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},s=[],i=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function l(){try{await i.$disconnect(),n.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){n.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{a.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),a.averageQueryTime=s.reduce((e,r)=>e+r,0)/s.length),e.duration&&e.duration>500&&n.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),n.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{l()})},30468:()=>{var e,r="https://js.stripe.com",t="".concat(r,"/").concat("basil","/stripe.js"),o=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,n=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,a=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var a,s=e[t];if(a=s.src,o.test(a)||n.test(a))return s}return null},s=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",o=document.createElement("script");o.src="".concat(t).concat(r);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(o),o},i=null,c=null,l=null;Promise.resolve().then(function(){return e||(e=(null!==i?i:(i=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,o=a();o?o&&null!==l&&null!==c&&(o.removeEventListener("load",l),o.removeEventListener("error",c),null===(t=o.parentNode)||void 0===t||t.removeChild(o),o=s(null)):o=s(null),l=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},c=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},o.addEventListener("load",l),o.addEventListener("error",c)}catch(e){r(e);return}})).catch(function(e){return i=null,Promise.reject(e)})).catch(function(r){return e=null,Promise.reject(r)}))}).catch(function(e){console.warn(e)})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,330,5609,1059],()=>t(13588));module.exports=o})();