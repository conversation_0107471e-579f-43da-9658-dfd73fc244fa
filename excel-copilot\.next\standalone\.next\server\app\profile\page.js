(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},74116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d}),r(85282),r(65675),r(12523);var a=r(23191),s=r(88716),i=r(37922),l=r.n(i),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85282)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],u=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\profile\\page.tsx"],c="/profile/page",f={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52738:(e,t,r)=>{Promise.resolve().then(r.bind(r,32220))},76464:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},37358:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},75290:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5932:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},31215:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},88378:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},63685:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},79635:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},94019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},32220:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eJ});var a=r(10326),s=r(17577),i=r(77109),l=r(35047),n=e=>"checkbox"===e.type,o=e=>e instanceof Date,d=e=>null==e;let u=e=>"object"==typeof e;var c=e=>!d(e)&&!Array.isArray(e)&&u(e)&&!o(e),f=e=>c(e)&&e.target?n(e.target)?e.target.checked:e.target.value:e,m=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,p=(e,t)=>e.has(m(t)),x=e=>{let t=e.constructor&&e.constructor.prototype;return c(t)&&t.hasOwnProperty("isPrototypeOf")},h="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function y(e){let t;let r=Array.isArray(e),a="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(h&&(e instanceof Blob||a))&&(r||c(e))))return e;else if(t=r?[]:{},r||x(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=y(e[r]));else t=e;return t}var g=e=>Array.isArray(e)?e.filter(Boolean):[],v=e=>void 0===e,b=(e,t,r)=>{if(!t||!c(e))return r;let a=g(t.split(/[,[\].]+?/)).reduce((e,t)=>d(e)?e:e[t],e);return v(a)||a===e?v(e[t])?r:e[t]:a},j=e=>"boolean"==typeof e,w=e=>/^\w*$/.test(e),N=e=>g(e.replace(/["|']|\]/g,"").split(/\.|\[/)),V=(e,t,r)=>{let a=-1,s=w(t)?[t]:N(t),i=s.length,l=i-1;for(;++a<i;){let t=s[a],i=r;if(a!==l){let r=e[t];i=c(r)||Array.isArray(r)?r:isNaN(+s[a+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let k={BLUR:"blur",FOCUS_OUT:"focusout"},_={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},D={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};s.createContext(null);var A=(e,t,r,a=!0)=>{let s={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(s,i,{get:()=>(t._proxyFormState[i]!==_.all&&(t._proxyFormState[i]=!a||_.all),r&&(r[i]=!0),e[i])});return s},C=e=>d(e)||!u(e);function F(e,t){if(C(e)||C(t))return e===t;if(o(e)&&o(t))return e.getTime()===t.getTime();let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let s of r){let r=e[s];if(!a.includes(s))return!1;if("ref"!==s){let e=t[s];if(o(r)&&o(e)||c(r)&&c(e)||Array.isArray(r)&&Array.isArray(e)?!F(r,e):r!==e)return!1}}return!0}var S=e=>"string"==typeof e,R=(e,t,r,a,s)=>S(e)?(a&&t.watch.add(e),b(r,e,s)):Array.isArray(e)?e.map(e=>(a&&t.watch.add(e),b(r,e))):(a&&(t.watchAll=!0),r),E=(e,t,r,a,s)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:s||!0}}:{},M=e=>Array.isArray(e)?e:[e],P=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},O=e=>c(e)&&!Object.keys(e).length,U=e=>"file"===e.type,Z=e=>"function"==typeof e,I=e=>{if(!h)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},T=e=>"select-multiple"===e.type,q=e=>"radio"===e.type,z=e=>q(e)||n(e),L=e=>I(e)&&e.isConnected;function B(e,t){let r=Array.isArray(t)?t:w(t)?[t]:N(t),a=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,a=0;for(;a<r;)e=v(e)?a++:e[t[a++]];return e}(e,r),s=r.length-1,i=r[s];return a&&delete a[i],0!==s&&(c(a)&&O(a)||Array.isArray(a)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(a))&&B(e,r.slice(0,-1)),e}var W=e=>{for(let t in e)if(Z(e[t]))return!0;return!1};function $(e,t={}){let r=Array.isArray(e);if(c(e)||r)for(let r in e)Array.isArray(e[r])||c(e[r])&&!W(e[r])?(t[r]=Array.isArray(e[r])?[]:{},$(e[r],t[r])):d(e[r])||(t[r]=!0);return t}var H=(e,t)=>(function e(t,r,a){let s=Array.isArray(t);if(c(t)||s)for(let s in t)Array.isArray(t[s])||c(t[s])&&!W(t[s])?v(r)||C(a[s])?a[s]=Array.isArray(t[s])?$(t[s],[]):{...$(t[s])}:e(t[s],d(r)?{}:r[s],a[s]):a[s]=!F(t[s],r[s]);return a})(e,t,$(t));let Y={value:!1,isValid:!1},G={value:!0,isValid:!0};var K=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?G:{value:e[0].value,isValid:!0}:G:Y}return Y},J=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:a})=>v(e)?e:t?""===e?NaN:e?+e:e:r&&S(e)?new Date(e):a?a(e):e;let Q={isValid:!1,value:null};var X=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Q):Q;function ee(e){let t=e.ref;return U(t)?t.files:q(t)?X(e.refs).value:T(t)?[...t.selectedOptions].map(({value:e})=>e):n(t)?K(e.refs).value:J(v(t.value)?e.ref.value:t.value,e)}var et=(e,t,r,a)=>{let s={};for(let r of e){let e=b(t,r);e&&V(s,r,e._f)}return{criteriaMode:r,names:[...e],fields:s,shouldUseNativeValidation:a}},er=e=>e instanceof RegExp,ea=e=>v(e)?e:er(e)?e.source:c(e)?er(e.value)?e.value.source:e.value:e,es=e=>({isOnSubmit:!e||e===_.onSubmit,isOnBlur:e===_.onBlur,isOnChange:e===_.onChange,isOnAll:e===_.all,isOnTouch:e===_.onTouched});let ei="AsyncFunction";var el=e=>!!e&&!!e.validate&&!!(Z(e.validate)&&e.validate.constructor.name===ei||c(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ei)),en=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eo=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ed=(e,t,r,a)=>{for(let s of r||Object.keys(e)){let r=b(e,s);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!a||e.ref&&t(e.ref,e.name)&&!a)return!0;if(ed(i,t))break}else if(c(i)&&ed(i,t))break}}};function eu(e,t,r){let a=b(e,r);if(a||w(r))return{error:a,name:r};let s=r.split(".");for(;s.length;){let a=s.join("."),i=b(t,a),l=b(e,a);if(i&&!Array.isArray(i)&&r!==a)break;if(l&&l.type)return{name:a,error:l};s.pop()}return{name:r}}var ec=(e,t,r,a)=>{r(e);let{name:s,...i}=e;return O(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!a||_.all))},ef=(e,t,r)=>!e||!t||e===t||M(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),em=(e,t,r,a,s)=>!s.isOnAll&&(!r&&s.isOnTouch?!(t||e):(r?a.isOnBlur:s.isOnBlur)?!e:(r?!a.isOnChange:!s.isOnChange)||e),ep=(e,t)=>!g(b(e,t)).length&&B(e,t),ex=(e,t,r)=>{let a=M(b(e,r));return V(a,"root",t[r]),V(e,r,a),e},eh=e=>S(e);function ey(e,t,r="validate"){if(eh(e)||Array.isArray(e)&&e.every(eh)||j(e)&&!e)return{type:r,message:eh(e)?e:"",ref:t}}var eg=e=>c(e)&&!er(e)?e:{value:e,message:""},ev=async(e,t,r,a,s,i)=>{let{ref:l,refs:o,required:u,maxLength:f,minLength:m,min:p,max:x,pattern:h,validate:y,name:g,valueAsNumber:w,mount:N}=e._f,V=b(r,g);if(!N||t.has(g))return{};let k=o?o[0]:l,_=e=>{s&&k.reportValidity&&(k.setCustomValidity(j(e)?"":e||""),k.reportValidity())},A={},C=q(l),F=n(l),R=(w||U(l))&&v(l.value)&&v(V)||I(l)&&""===l.value||""===V||Array.isArray(V)&&!V.length,M=E.bind(null,g,a,A),P=(e,t,r,a=D.maxLength,s=D.minLength)=>{let i=e?t:r;A[g]={type:e?a:s,message:i,ref:l,...M(e?a:s,i)}};if(i?!Array.isArray(V)||!V.length:u&&(!(C||F)&&(R||d(V))||j(V)&&!V||F&&!K(o).isValid||C&&!X(o).isValid)){let{value:e,message:t}=eh(u)?{value:!!u,message:u}:eg(u);if(e&&(A[g]={type:D.required,message:t,ref:k,...M(D.required,t)},!a))return _(t),A}if(!R&&(!d(p)||!d(x))){let e,t;let r=eg(x),s=eg(p);if(d(V)||isNaN(V)){let a=l.valueAsDate||new Date(V),i=e=>new Date(new Date().toDateString()+" "+e),n="time"==l.type,o="week"==l.type;S(r.value)&&V&&(e=n?i(V)>i(r.value):o?V>r.value:a>new Date(r.value)),S(s.value)&&V&&(t=n?i(V)<i(s.value):o?V<s.value:a<new Date(s.value))}else{let a=l.valueAsNumber||(V?+V:V);d(r.value)||(e=a>r.value),d(s.value)||(t=a<s.value)}if((e||t)&&(P(!!e,r.message,s.message,D.max,D.min),!a))return _(A[g].message),A}if((f||m)&&!R&&(S(V)||i&&Array.isArray(V))){let e=eg(f),t=eg(m),r=!d(e.value)&&V.length>+e.value,s=!d(t.value)&&V.length<+t.value;if((r||s)&&(P(r,e.message,t.message),!a))return _(A[g].message),A}if(h&&!R&&S(V)){let{value:e,message:t}=eg(h);if(er(e)&&!V.match(e)&&(A[g]={type:D.pattern,message:t,ref:l,...M(D.pattern,t)},!a))return _(t),A}if(y){if(Z(y)){let e=ey(await y(V,r),k);if(e&&(A[g]={...e,...M(D.validate,e.message)},!a))return _(e.message),A}else if(c(y)){let e={};for(let t in y){if(!O(e)&&!a)break;let s=ey(await y[t](V,r),k,t);s&&(e={...s,...M(t,s.message)},_(s.message),a&&(A[g]=e))}if(!O(e)&&(A[g]={ref:k,...e},!a))return A}}return _(!0),A};let eb={mode:_.onSubmit,reValidateMode:_.onChange,shouldFocusError:!0},ej="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,ew=(e,t,r)=>{if(e&&"reportValidity"in e){let a=b(r,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},eN=(e,t)=>{for(let r in t.fields){let a=t.fields[r];a&&a.ref&&"reportValidity"in a.ref?ew(a.ref,r,e):a.refs&&a.refs.forEach(t=>ew(t,r,e))}},eV=(e,t)=>{t.shouldUseNativeValidation&&eN(e,t);let r={};for(let a in e){let s=b(t.fields,a),i=Object.assign(e[a]||{},{ref:s&&s.ref});if(ek(t.names||Object.keys(e),a)){let e=Object.assign({},b(r,a));V(e,"root",i),V(r,a,e)}else V(r,a,i)}return r},ek=(e,t)=>e.some(e=>e.startsWith(t+"."));var e_=function(e,t){for(var r={};e.length;){var a=e[0],s=a.code,i=a.message,l=a.path.join(".");if(!r[l]){if("unionErrors"in a){var n=a.unionErrors[0].errors[0];r[l]={message:n.message,type:n.code}}else r[l]={message:i,type:s}}if("unionErrors"in a&&a.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var o=r[l].types,d=o&&o[a.code];r[l]=E(l,t,r,s,d?[].concat(d,a.message):a.message)}e.shift()}return r},eD=r(27256),eA=r(85999),eC=r(75290),eF=r(58038),eS=r(31215),eR=r(76464),eE=r(79635),eM=r(37358),eP=r(5932),eO=r(88378),eU=r(91664),eZ=r(29752),eI=r(41190),eT=r(44794),eq=r(38443),ez=r(94866),eL=r(50384);let eB=(0,r(76557).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var eW=r(63685),e$=r(94019),eH=r(3594),eY=r(24118);function eG({currentImage:e,userName:t,onImageUpdate:r,className:i}){let[l,n]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1),[u,c]=(0,s.useState)(e||""),[f,m]=(0,s.useState)(null),p=(0,s.useRef)(null),x=e=>e?e.split(" ").map(e=>e[0]).join("").toUpperCase().substring(0,2):"?",h=e=>["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type)?!(e.size>5242880)||(eA.toast.error("Arquivo muito grande. M\xe1ximo 5MB."),!1):(eA.toast.error("Formato n\xe3o suportado. Use JPEG, PNG ou WebP."),!1),y=async e=>{if(h(e)){d(!0);try{let a=new FileReader;a.onload=e=>{m(e.target?.result)},a.readAsDataURL(e),await new Promise(e=>setTimeout(e,2e3));let s=`https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(t||"User")}&backgroundColor=random`;c(s),eA.toast.success("Avatar atualizado com sucesso!"),r(s),n(!1)}catch(e){eA.toast.error("Erro ao fazer upload da imagem")}finally{d(!1),m(null)}}},g=async()=>{if(!u.trim()){eA.toast.error("Digite uma URL v\xe1lida");return}try{let e=new Image;e.onload=()=>{r(u),eA.toast.success("Avatar atualizado com sucesso!"),n(!1)},e.onerror=()=>{eA.toast.error("URL de imagem inv\xe1lida")},e.src=u}catch(e){eA.toast.error("Erro ao validar imagem")}};return(0,a.jsxs)(eY.Vq,{open:l,onOpenChange:n,children:[a.jsx(eY.hg,{asChild:!0,children:(0,a.jsxs)("div",{className:`relative group cursor-pointer ${i}`,children:[(0,a.jsxs)(eH.qE,{className:"h-20 w-20",children:[a.jsx(eH.F$,{src:e||"",alt:t||"Usu\xe1rio"}),a.jsx(eH.Q5,{className:"text-lg",children:x(t)})]}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:a.jsx(eB,{className:"h-6 w-6 text-white"})})]})}),(0,a.jsxs)(eY.cZ,{className:"sm:max-w-md",children:[(0,a.jsxs)(eY.fK,{children:[a.jsx(eY.$N,{children:"Atualizar Avatar"}),a.jsx(eY.Be,{children:"Fa\xe7a upload de uma nova imagem ou cole uma URL"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)(eH.qE,{className:"h-24 w-24",children:[a.jsx(eH.F$,{src:f||e||"",alt:t||"Usu\xe1rio"}),a.jsx(eH.Q5,{className:"text-xl",children:x(t)})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(eT._,{children:"Fazer Upload"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(eU.Button,{variant:"outline",onClick:()=>p.current?.click(),disabled:o,className:"flex-1",children:[o?a.jsx(eC.Z,{className:"h-4 w-4 animate-spin mr-2"}):a.jsx(eW.Z,{className:"h-4 w-4 mr-2"}),o?"Enviando...":"Escolher Arquivo"]}),a.jsx("input",{ref:p,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];t&&y(t)},className:"hidden"})]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"JPEG, PNG ou WebP. M\xe1ximo 5MB."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(eT._,{htmlFor:"imageUrl",children:"URL da Imagem"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(eI.I,{id:"imageUrl",value:u,onChange:e=>c(e.target.value),placeholder:"https://exemplo.com/imagem.jpg",className:"flex-1"}),a.jsx(eU.Button,{variant:"outline",onClick:g,disabled:!u.trim(),children:"Aplicar"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)(eU.Button,{variant:"outline",onClick:()=>{c(""),r(""),eA.toast.success("Avatar removido"),n(!1)},className:"text-red-600 hover:text-red-700",children:[a.jsx(e$.Z,{className:"h-4 w-4 mr-2"}),"Remover"]}),a.jsx(eU.Button,{variant:"outline",onClick:()=>n(!1),children:"Cancelar"})]})]})]})]})}let eK=eD.z.object({name:eD.z.string().min(1,"Nome \xe9 obrigat\xf3rio").max(100,"Nome muito longo"),email:eD.z.string().email("Email inv\xe1lido"),image:eD.z.string().url("URL de imagem inv\xe1lida").optional().or(eD.z.literal(""))});function eJ(){var e;let{data:t,status:r}=(0,i.useSession)();(0,l.useRouter)();let[u,m]=(0,s.useState)(null),[x,w]=(0,s.useState)(!0),[N,D]=(0,s.useState)(!1),{register:C,handleSubmit:E,setValue:q,formState:{errors:W,isDirty:$}}=function(e={}){let t=s.useRef(void 0),r=s.useRef(void 0),[a,i]=s.useState({isDirty:!1,isValidating:!1,isLoading:Z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Z(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...eb,...e},a={submitCount:0,isDirty:!1,isReady:!1,isLoading:Z(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},s={},i=(c(r.defaultValues)||c(r.values))&&y(r.values||r.defaultValues)||{},l=r.shouldUnregister?{}:y(i),u={action:!1,mount:!1,watch:!1},m={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0,w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},N={...w},D={array:P(),state:P()},A=es(r.mode),C=es(r.reValidateMode),E=r.criteriaMode===_.all,q=e=>t=>{clearTimeout(x),x=setTimeout(e,t)},W=async e=>{if(!r.disabled&&(w.isValid||N.isValid||e)){let e=r.resolver?O((await X()).errors):await ei(s,!0);e!==a.isValid&&D.state.next({isValid:e})}},$=(e,t)=>{!r.disabled&&(w.isValidating||w.validatingFields||N.isValidating||N.validatingFields)&&((e||Array.from(m.mount)).forEach(e=>{e&&(t?V(a.validatingFields,e,t):B(a.validatingFields,e))}),D.state.next({validatingFields:a.validatingFields,isValidating:!O(a.validatingFields)}))},Y=(e,t)=>{V(a.errors,e,t),D.state.next({errors:a.errors})},G=(e,t,r,a)=>{let n=b(s,e);if(n){let s=b(l,e,v(r)?b(i,e):r);v(s)||a&&a.defaultChecked||t?V(l,e,t?s:ee(n._f)):eg(e,s),u.mount&&W()}},K=(e,t,s,l,n)=>{let o=!1,d=!1,u={name:e};if(!r.disabled){if(!s||l){(w.isDirty||N.isDirty)&&(d=a.isDirty,a.isDirty=u.isDirty=eh(),o=d!==u.isDirty);let r=F(b(i,e),t);d=!!b(a.dirtyFields,e),r?B(a.dirtyFields,e):V(a.dirtyFields,e,!0),u.dirtyFields=a.dirtyFields,o=o||(w.dirtyFields||N.dirtyFields)&&!r!==d}if(s){let t=b(a.touchedFields,e);t||(V(a.touchedFields,e,s),u.touchedFields=a.touchedFields,o=o||(w.touchedFields||N.touchedFields)&&t!==s)}o&&n&&D.state.next(u)}return o?u:{}},Q=(e,s,i,l)=>{let n=b(a.errors,e),o=(w.isValid||N.isValid)&&j(s)&&a.isValid!==s;if(r.delayError&&i?(t=q(()=>Y(e,i)))(r.delayError):(clearTimeout(x),t=null,i?V(a.errors,e,i):B(a.errors,e)),(i?!F(n,i):n)||!O(l)||o){let t={...l,...o&&j(s)?{isValid:s}:{},errors:a.errors,name:e};a={...a,...t},D.state.next(t)}},X=async e=>{$(e,!0);let t=await r.resolver(l,r.context,et(e||m.mount,s,r.criteriaMode,r.shouldUseNativeValidation));return $(e),t},er=async e=>{let{errors:t}=await X(e);if(e)for(let r of e){let e=b(t,r);e?V(a.errors,r,e):B(a.errors,r)}else a.errors=t;return t},ei=async(e,t,s={valid:!0})=>{for(let i in e){let n=e[i];if(n){let{_f:e,...o}=n;if(e){let o=m.array.has(e.name),d=n._f&&el(n._f);d&&w.validatingFields&&$([i],!0);let u=await ev(n,m.disabled,l,E,r.shouldUseNativeValidation&&!t,o);if(d&&w.validatingFields&&$([i]),u[e.name]&&(s.valid=!1,t))break;t||(b(u,e.name)?o?ex(a.errors,u,e.name):V(a.errors,e.name,u[e.name]):B(a.errors,e.name))}O(o)||await ei(o,t,s)}}return s.valid},eh=(e,t)=>!r.disabled&&(e&&t&&V(l,e,t),!F(e_(),i)),ey=(e,t,r)=>R(e,m,{...u.mount?l:v(t)?i:S(e)?{[e]:t}:t},r,t),eg=(e,t,r={})=>{let a=b(s,e),i=t;if(a){let r=a._f;r&&(r.disabled||V(l,e,J(t,r)),i=I(r.ref)&&d(t)?"":t,T(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?n(r.ref)?r.refs.length>1?r.refs.forEach(e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(i)?!!i.find(t=>t===e.value):i===e.value)):r.refs[0]&&(r.refs[0].checked=!!i):r.refs.forEach(e=>e.checked=e.value===i):U(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||D.state.next({name:e,values:y(l)})))}(r.shouldDirty||r.shouldTouch)&&K(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ek(e)},ej=(e,t,r)=>{for(let a in t){let i=t[a],l=`${e}.${a}`,n=b(s,l);(m.array.has(e)||c(i)||n&&!n._f)&&!o(i)?ej(l,i,r):eg(l,i,r)}},ew=(e,t,r={})=>{let n=b(s,e),o=m.array.has(e),c=y(t);V(l,e,c),o?(D.array.next({name:e,values:y(l)}),(w.isDirty||w.dirtyFields||N.isDirty||N.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:H(i,l),isDirty:eh(e,c)})):!n||n._f||d(c)?eg(e,c,r):ej(e,c,r),eo(e,m)&&D.state.next({...a}),D.state.next({name:u.mount?e:void 0,values:y(l)})},eN=async e=>{u.mount=!0;let i=e.target,n=i.name,d=!0,c=b(s,n),p=e=>{d=Number.isNaN(e)||o(e)&&isNaN(e.getTime())||F(e,b(l,n,e))};if(c){let o,u;let x=i.type?ee(c._f):f(e),h=e.type===k.BLUR||e.type===k.FOCUS_OUT,g=!en(c._f)&&!r.resolver&&!b(a.errors,n)&&!c._f.deps||em(h,b(a.touchedFields,n),a.isSubmitted,C,A),v=eo(n,m,h);V(l,n,x),h?(c._f.onBlur&&c._f.onBlur(e),t&&t(0)):c._f.onChange&&c._f.onChange(e);let j=K(n,x,h),_=!O(j)||v;if(h||D.state.next({name:n,type:e.type,values:y(l)}),g)return(w.isValid||N.isValid)&&("onBlur"===r.mode?h&&W():h||W()),_&&D.state.next({name:n,...v?{}:j});if(!h&&v&&D.state.next({...a}),r.resolver){let{errors:e}=await X([n]);if(p(x),d){let t=eu(a.errors,s,n),r=eu(e,s,t.name||n);o=r.error,n=r.name,u=O(e)}}else $([n],!0),o=(await ev(c,m.disabled,l,E,r.shouldUseNativeValidation))[n],$([n]),p(x),d&&(o?u=!1:(w.isValid||N.isValid)&&(u=await ei(s,!0)));d&&(c._f.deps&&ek(c._f.deps),Q(n,u,o,j))}},eV=(e,t)=>{if(b(a.errors,t)&&e.focus)return e.focus(),1},ek=async(e,t={})=>{let i,l;let n=M(e);if(r.resolver){let t=await er(v(e)?e:n);i=O(t),l=e?!n.some(e=>b(t,e)):i}else e?((l=(await Promise.all(n.map(async e=>{let t=b(s,e);return await ei(t&&t._f?{[e]:t}:t)}))).every(Boolean))||a.isValid)&&W():l=i=await ei(s);return D.state.next({...!S(e)||(w.isValid||N.isValid)&&i!==a.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:a.errors}),t.shouldFocus&&!l&&ed(s,eV,e?n:m.mount),l},e_=e=>{let t={...u.mount?l:i};return v(e)?t:S(e)?b(t,e):e.map(e=>b(t,e))},eD=(e,t)=>({invalid:!!b((t||a).errors,e),isDirty:!!b((t||a).dirtyFields,e),error:b((t||a).errors,e),isValidating:!!b(a.validatingFields,e),isTouched:!!b((t||a).touchedFields,e)}),eA=(e,t,r)=>{let i=(b(s,e,{_f:{}})._f||{}).ref,{ref:l,message:n,type:o,...d}=b(a.errors,e)||{};V(a.errors,e,{...d,...t,ref:i}),D.state.next({name:e,errors:a.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eC=e=>D.state.subscribe({next:t=>{ef(e.name,t.name,e.exact)&&ec(t,e.formState||w,eU,e.reRenderRoot)&&e.callback({values:{...l},...a,...t})}}).unsubscribe,eF=(e,t={})=>{for(let n of e?M(e):m.mount)m.mount.delete(n),m.array.delete(n),t.keepValue||(B(s,n),B(l,n)),t.keepError||B(a.errors,n),t.keepDirty||B(a.dirtyFields,n),t.keepTouched||B(a.touchedFields,n),t.keepIsValidating||B(a.validatingFields,n),r.shouldUnregister||t.keepDefaultValue||B(i,n);D.state.next({values:y(l)}),D.state.next({...a,...t.keepDirty?{isDirty:eh()}:{}}),t.keepIsValid||W()},eS=({disabled:e,name:t})=>{(j(e)&&u.mount||e||m.disabled.has(t))&&(e?m.disabled.add(t):m.disabled.delete(t))},eR=(e,t={})=>{let a=b(s,e),l=j(t.disabled)||j(r.disabled);return V(s,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),m.mount.add(e),a?eS({disabled:j(t.disabled)?t.disabled:r.disabled,name:e}):G(e,!0,t.value),{...l?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ea(t.min),max:ea(t.max),minLength:ea(t.minLength),maxLength:ea(t.maxLength),pattern:ea(t.pattern)}:{},name:e,onChange:eN,onBlur:eN,ref:l=>{if(l){eR(e,t),a=b(s,e);let r=v(l.value)&&l.querySelectorAll&&l.querySelectorAll("input,select,textarea")[0]||l,n=z(r),o=a._f.refs||[];(n?o.find(e=>e===r):r===a._f.ref)||(V(s,e,{_f:{...a._f,...n?{refs:[...o.filter(L),r,...Array.isArray(b(i,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),G(e,!1,void 0,r))}else(a=b(s,e,{}))._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(p(m.array,e)&&u.action)&&m.unMount.add(e)}}},eE=()=>r.shouldFocusError&&ed(s,eV,m.mount),eM=(e,t)=>async i=>{let n;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let o=y(l);if(D.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await X();a.errors=e,o=t}else await ei(s);if(m.disabled.size)for(let e of m.disabled)V(o,e,void 0);if(B(a.errors,"root"),O(a.errors)){D.state.next({errors:{}});try{await e(o,i)}catch(e){n=e}}else t&&await t({...a.errors},i),eE(),setTimeout(eE);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:O(a.errors)&&!n,submitCount:a.submitCount+1,errors:a.errors}),n)throw n},eP=(e,t={})=>{let n=e?y(e):i,o=y(n),d=O(e),c=d?i:o;if(t.keepDefaultValues||(i=n),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...m.mount,...Object.keys(H(i,l))])))b(a.dirtyFields,e)?V(c,e,b(l,e)):ew(e,b(c,e));else{if(h&&v(e))for(let e of m.mount){let t=b(s,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(I(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of m.mount)ew(e,b(c,e))}l=y(c),D.array.next({values:{...c}}),D.state.next({values:{...c}})}m={mount:t.keepDirtyValues?m.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},u.mount=!w.isValid||!!t.keepIsValid||!!t.keepDirtyValues,u.watch=!!r.shouldUnregister,D.state.next({submitCount:t.keepSubmitCount?a.submitCount:0,isDirty:!d&&(t.keepDirty?a.isDirty:!!(t.keepDefaultValues&&!F(e,i))),isSubmitted:!!t.keepIsSubmitted&&a.isSubmitted,dirtyFields:d?{}:t.keepDirtyValues?t.keepDefaultValues&&l?H(i,l):a.dirtyFields:t.keepDefaultValues&&e?H(i,e):t.keepDirty?a.dirtyFields:{},touchedFields:t.keepTouched?a.touchedFields:{},errors:t.keepErrors?a.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&a.isSubmitSuccessful,isSubmitting:!1})},eO=(e,t)=>eP(Z(e)?e(l):e,t),eU=e=>{a={...a,...e}},eZ={control:{register:eR,unregister:eF,getFieldState:eD,handleSubmit:eM,setError:eA,_subscribe:eC,_runSchema:X,_getWatch:ey,_getDirty:eh,_setValid:W,_setFieldArray:(e,t=[],n,o,d=!0,c=!0)=>{if(o&&n&&!r.disabled){if(u.action=!0,c&&Array.isArray(b(s,e))){let t=n(b(s,e),o.argA,o.argB);d&&V(s,e,t)}if(c&&Array.isArray(b(a.errors,e))){let t=n(b(a.errors,e),o.argA,o.argB);d&&V(a.errors,e,t),ep(a.errors,e)}if((w.touchedFields||N.touchedFields)&&c&&Array.isArray(b(a.touchedFields,e))){let t=n(b(a.touchedFields,e),o.argA,o.argB);d&&V(a.touchedFields,e,t)}(w.dirtyFields||N.dirtyFields)&&(a.dirtyFields=H(i,l)),D.state.next({name:e,isDirty:eh(e,t),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else V(l,e,t)},_setDisabledField:eS,_setErrors:e=>{a.errors=e,D.state.next({errors:a.errors,isValid:!1})},_getFieldArray:e=>g(b(u.mount?l:i,e,r.shouldUnregister?b(i,e,[]):[])),_reset:eP,_resetDefaultValues:()=>Z(r.defaultValues)&&r.defaultValues().then(e=>{eO(e,r.resetOptions),D.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of m.unMount){let t=b(s,e);t&&(t._f.refs?t._f.refs.every(e=>!L(e)):!L(t._f.ref))&&eF(e)}m.unMount=new Set},_disableForm:e=>{j(e)&&(D.state.next({disabled:e}),ed(s,(t,r)=>{let a=b(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:D,_proxyFormState:w,get _fields(){return s},get _formValues(){return l},get _state(){return u},set _state(value){u=value},get _defaultValues(){return i},get _names(){return m},set _names(value){m=value},get _formState(){return a},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(u.mount=!0,N={...N,...e.formState},eC({...e,formState:N})),trigger:ek,register:eR,handleSubmit:eM,watch:(e,t)=>Z(e)?D.state.subscribe({next:r=>e(ey(void 0,t),r)}):ey(e,t,!0),setValue:ew,getValues:e_,reset:eO,resetField:(e,t={})=>{b(s,e)&&(v(t.defaultValue)?ew(e,y(b(i,e))):(ew(e,t.defaultValue),V(i,e,y(t.defaultValue))),t.keepTouched||B(a.touchedFields,e),t.keepDirty||(B(a.dirtyFields,e),a.isDirty=t.defaultValue?eh(e,y(b(i,e))):eh()),!t.keepError&&(B(a.errors,e),w.isValid&&W()),D.state.next({...a}))},clearErrors:e=>{e&&M(e).forEach(e=>B(a.errors,e)),D.state.next({errors:e?a.errors:{}})},unregister:eF,setError:eA,setFocus:(e,t={})=>{let r=b(s,e),a=r&&r._f;if(a){let e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&Z(e.select)&&e.select())}},getFieldState:eD};return{...eZ,formControl:eZ}}(e),formState:a},e.formControl&&e.defaultValues&&!Z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let l=t.current.control;return l._options=e,ej(()=>{let e=l._subscribe({formState:l._proxyFormState,callback:()=>i({...l._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),l._formState.isReady=!0,e},[l]),s.useEffect(()=>l._disableForm(e.disabled),[l,e.disabled]),s.useEffect(()=>{e.mode&&(l._options.mode=e.mode),e.reValidateMode&&(l._options.reValidateMode=e.reValidateMode),e.errors&&!O(e.errors)&&l._setErrors(e.errors)},[l,e.errors,e.mode,e.reValidateMode]),s.useEffect(()=>{e.shouldUnregister&&l._subjects.state.next({values:l._getWatch()})},[l,e.shouldUnregister]),s.useEffect(()=>{if(l._proxyFormState.isDirty){let e=l._getDirty();e!==a.isDirty&&l._subjects.state.next({isDirty:e})}},[l,a.isDirty]),s.useEffect(()=>{e.values&&!F(e.values,r.current)?(l._reset(e.values,l._options.resetOptions),r.current=e.values,i(e=>({...e}))):l._resetDefaultValues()},[l,e.values]),s.useEffect(()=>{l._state.mount||(l._setValid(),l._state.mount=!0),l._state.watch&&(l._state.watch=!1,l._subjects.state.next({...l._formState})),l._removeUnmounted()}),t.current.formState=A(a,l),t.current}({resolver:(void 0===e&&(e={}),function(t,r,a){try{return Promise.resolve(function(r,s){try{var i=Promise.resolve(eK["sync"===e.mode?"parse":"parseAsync"](t,void 0)).then(function(r){return a.shouldUseNativeValidation&&eN({},a),{errors:{},values:e.raw?t:r}})}catch(e){return s(e)}return i&&i.then?i.then(void 0,s):i}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:eV(e_(e.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(e){return Promise.reject(e)}})}),Y=async e=>{D(!0);try{let t=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();t.ok?(eA.toast.success("Perfil atualizado com sucesso!"),u&&m({...u,...r.profile})):eA.toast.error("Erro ao atualizar perfil: "+r.error)}catch(e){eA.toast.error("Erro ao atualizar perfil")}finally{D(!1)}},G=async e=>{try{let t=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:e})}),r=await t.json();t.ok&&u?(m({...u,image:e}),q("image",e)):eA.toast.error("Erro ao atualizar avatar: "+r.error)}catch(e){eA.toast.error("Erro ao atualizar avatar")}},K=e=>e?new Date(e).toLocaleDateString("pt-BR"):"Nunca";return"loading"===r||x?a.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx(eC.Z,{className:"h-8 w-8 animate-spin"})})}):t&&u?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[a.jsx(eG,{currentImage:u.image,userName:u.name,onImageUpdate:G}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold",children:u.name||"Usu\xe1rio"}),a.jsx("p",{className:"text-muted-foreground",children:u.email}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[u.subscription&&a.jsx(eq.C,{variant:"secondary",children:u.subscription.plan.toUpperCase()}),u.emailVerified&&(0,a.jsxs)(eq.C,{variant:"outline",className:"text-green-600",children:[a.jsx(eF.Z,{className:"h-3 w-3 mr-1"}),"Verificado"]})]})]})]}),(0,a.jsxs)(eL.mQ,{defaultValue:"profile",className:"space-y-6",children:[(0,a.jsxs)(eL.dr,{children:[a.jsx(eL.SP,{value:"profile",children:"Perfil"}),a.jsx(eL.SP,{value:"stats",children:"Estat\xedsticas"}),a.jsx(eL.SP,{value:"preferences",children:"Prefer\xeancias"})]}),a.jsx(eL.nU,{value:"profile",children:(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{children:[a.jsx(eZ.ll,{children:"Informa\xe7\xf5es do Perfil"}),a.jsx(eZ.SZ,{children:"Atualize suas informa\xe7\xf5es pessoais"})]}),a.jsx(eZ.aY,{children:(0,a.jsxs)("form",{onSubmit:E(Y),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(eT._,{htmlFor:"name",children:"Nome"}),a.jsx(eI.I,{id:"name",...C("name"),placeholder:"Seu nome completo"}),W.name&&a.jsx("p",{className:"text-sm text-red-500",children:W.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(eT._,{htmlFor:"email",children:"Email"}),a.jsx(eI.I,{id:"email",type:"email",...C("email"),placeholder:"<EMAIL>"}),W.email&&a.jsx("p",{className:"text-sm text-red-500",children:W.email.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(eT._,{htmlFor:"image",children:"URL da Imagem"}),a.jsx(eI.I,{id:"image",...C("image"),placeholder:"https://exemplo.com/sua-foto.jpg"}),W.image&&a.jsx("p",{className:"text-sm text-red-500",children:W.image.message})]}),(0,a.jsxs)(eU.Button,{type:"submit",disabled:!$||N,className:"w-full sm:w-auto",children:[N?a.jsx(eC.Z,{className:"h-4 w-4 animate-spin mr-2"}):a.jsx(eS.Z,{className:"h-4 w-4 mr-2"}),"Salvar Altera\xe7\xf5es"]})]})})]})}),a.jsx(eL.nU,{value:"stats",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(eZ.ll,{className:"text-sm font-medium",children:"Planilhas Criadas"}),a.jsx(eR.Z,{className:"h-4 w-4 text-muted-foreground"})]}),a.jsx(eZ.aY,{children:a.jsx("div",{className:"text-2xl font-bold",children:u.stats.workbooksCount})})]}),(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(eZ.ll,{className:"text-sm font-medium",children:"Total de Logins"}),a.jsx(eE.Z,{className:"h-4 w-4 text-muted-foreground"})]}),a.jsx(eZ.aY,{children:a.jsx("div",{className:"text-2xl font-bold",children:u.stats.loginCount})})]}),(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(eZ.ll,{className:"text-sm font-medium",children:"Membro Desde"}),a.jsx(eM.Z,{className:"h-4 w-4 text-muted-foreground"})]}),a.jsx(eZ.aY,{children:a.jsx("div",{className:"text-2xl font-bold",children:K(u.stats.memberSince)})})]}),(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[a.jsx(eZ.ll,{className:"text-sm font-medium",children:"\xdaltimo Login"}),a.jsx(eP.Z,{className:"h-4 w-4 text-muted-foreground"})]}),a.jsx(eZ.aY,{children:a.jsx("div",{className:"text-2xl font-bold",children:K(u.stats.lastLogin)})})]})]})}),a.jsx(eL.nU,{value:"preferences",children:(0,a.jsxs)(eZ.Zb,{children:[(0,a.jsxs)(eZ.Ol,{children:[a.jsx(eZ.ll,{children:"Prefer\xeancias"}),a.jsx(eZ.SZ,{children:"Configure suas prefer\xeancias de uso"})]}),a.jsx(eZ.aY,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium mb-2",children:"Tema"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Atual: ",u.preferences.theme]})]}),a.jsx(ez.Z,{}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium mb-2",children:"Idioma"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Atual: ",u.preferences.language]})]}),a.jsx(ez.Z,{}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium mb-2",children:"Notifica\xe7\xf5es"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["Email: ",u.preferences.notifications.email?"Ativado":"Desativado"]}),(0,a.jsxs)("p",{children:["Push: ",u.preferences.notifications.push?"Ativado":"Desativado"]}),(0,a.jsxs)("p",{children:["Marketing: ",u.preferences.notifications.marketing?"Ativado":"Desativado"]})]})]}),a.jsx(ez.Z,{}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-medium mb-2",children:"Privacidade"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["Perfil Vis\xedvel: ",u.preferences.privacy.profileVisible?"Sim":"N\xe3o"]}),(0,a.jsxs)("p",{children:["Compartilhar Dados de Uso: ",u.preferences.privacy.shareUsageData?"Sim":"N\xe3o"]})]})]}),(0,a.jsxs)(eU.Button,{variant:"outline",className:"w-full sm:w-auto",children:[a.jsx(eO.Z,{className:"h-4 w-4 mr-2"}),"Configurar Prefer\xeancias"]})]})})]})})]})]}):null}},38443:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});var a=r(10326),s=r(79360);r(17577);var i=r(51223);let l=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...r}){return a.jsx("div",{className:(0,i.cn)(l({variant:t}),e),...r})}},29752:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>d,SZ:()=>c,Zb:()=>o,aY:()=>f,eW:()=>m,ll:()=>u});var a=r(10326),s=r(31722),i=r(17577),l=r(45365),n=r(51223);let o=(0,i.forwardRef)(({className:e,children:t,hoverable:r=!1,variant:i="default",noPadding:o=!1,animated:d=!1,...u},c)=>{let f=(0,n.cn)("rounded-xl border shadow-sm",{"p-6":!o,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":r&&!d,"border-border bg-card":"default"===i,"border-border/50 bg-transparent":"outline"===i,"bg-card/90 backdrop-blur-md border-border/50":"glass"===i,"bg-gradient-primary text-primary-foreground border-none":"gradient"===i},e);return d?a.jsx(s.E.div,{ref:c,className:f,...(0,l.Ph)("card"),whileHover:r?l.q.hover:void 0,whileTap:r?l.q.tap:void 0,...u,children:t}):a.jsx("div",{ref:c,className:f,...u,children:t})});o.displayName="Card";let d=(0,i.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("mb-4 flex flex-col space-y-1.5",e),...t}));d.displayName="CardHeader";let u=(0,i.forwardRef)(({className:e,...t},r)=>a.jsx("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));u.displayName="CardTitle";let c=(0,i.forwardRef)(({className:e,...t},r)=>a.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let f=(0,i.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("card-content",e),...t}));f.displayName="CardContent";let m=(0,i.forwardRef)(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("flex items-center pt-4 mt-auto",e),...t}));m.displayName="CardFooter"},24118:(e,t,r)=>{"use strict";r.d(t,{$N:()=>x,Be:()=>h,Vq:()=>o,cN:()=>p,cZ:()=>f,fK:()=>m,hg:()=>d});var a=r(10326),s=r(98958),i=r(94019),l=r(17577),n=r(51223);let o=s.fC,d=s.xz,u=s.h_;s.x8;let c=l.forwardRef(({className:e,...t},r)=>a.jsx(s.aV,{ref:r,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=s.aV.displayName;let f=l.forwardRef(({className:e,children:t,...r},l)=>(0,a.jsxs)(u,{children:[a.jsx(c,{}),(0,a.jsxs)(s.VY,{ref:l,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,a.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=s.VY.displayName;let m=({className:e,...t})=>a.jsx("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let p=({className:e,...t})=>a.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="DialogFooter";let x=l.forwardRef(({className:e,...t},r)=>a.jsx(s.Dx,{ref:r,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=s.Dx.displayName;let h=l.forwardRef(({className:e,...t},r)=>a.jsx(s.dk,{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=s.dk.displayName},62734:(e,t,r)=>{"use strict";r.d(t,{RM:()=>o,aF:()=>d});var a=r(17577),s=r.n(a),i=r(51223);let l={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},n={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function o(e="default",t="md",r=!1,a){return(0,i.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l[e],n[t],r&&"min-h-[80px] resize-vertical",a)}function d(e,t){return t?s().createElement("div",{className:t},e):e}},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>l,Z:()=>n});var a=r(10326),s=r(17577),i=r(62734);let l=s.forwardRef(({className:e,type:t,wrapperClassName:r,variant:s="default",fieldSize:l="md",inputSize:n,...o},d)=>{let u=a.jsx("input",{type:t,className:(0,i.RM)(s,n||l,!1,e),ref:d,...o});return(0,i.aF)(u,r)});l.displayName="Input";let n=l},44794:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var a=r(10326),s=r(34478),i=r(79360),l=r(17577),n=r(51223);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef(({className:e,...t},r)=>a.jsx(s.f,{ref:r,className:(0,n.cn)(o(),e),...t}));d.displayName=s.f.displayName},94866:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var a=r(10326),s=r(17577);r(60962);var i=r(48051),l=Symbol("radix.slottable");function n(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){let e,l;let n=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let a in t){let s=e[a],i=t[a];/^on[A-Z]/.test(a)?s&&i?r[a]=(...e)=>{let t=i(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...i}:"className"===a&&(r[a]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(o.ref=t?(0,i.F)(t,n):n),s.cloneElement(r,o)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:i,...l}=e,o=s.Children.toArray(i),d=o.find(n);if(d){let e=d.props.children,i=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...l,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=s.forwardRef((e,s)=>{let{asChild:i,...l}=e,n=i?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n,{...l,ref:s})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),d="horizontal",u=["horizontal","vertical"],c=s.forwardRef((e,t)=>{let{decorative:r,orientation:s=d,...i}=e,l=u.includes(s)?s:d;return(0,a.jsx)(o.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...i,ref:t})});c.displayName="Separator";var f=r(51223);let m=s.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...s},i)=>a.jsx(c,{ref:i,decorative:r,orientation:t,className:(0,f.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...s}));m.displayName=c.displayName},50384:(e,t,r)=>{"use strict";r.d(t,{SP:()=>d,dr:()=>o,mQ:()=>n,nU:()=>u});var a=r(10326),s=r(28407),i=r(17577),l=r(51223);let n=s.fC,o=i.forwardRef(({className:e,...t},r)=>a.jsx(s.aV,{ref:r,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));o.displayName=s.aV.displayName;let d=i.forwardRef(({className:e,...t},r)=>a.jsx(s.xz,{ref:r,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=s.xz.displayName;let u=i.forwardRef(({className:e,...t},r)=>a.jsx(s.VY,{ref:r,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));u.displayName=s.VY.displayName},38238:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let a=Reflect.get(e,t,r);return"function"==typeof a?a.bind(e):a}static set(e,t,r,a){return Reflect.set(e,t,r,a)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},85282:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var a=r(68570);let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\profile\page.tsx`),{__esModule:i,$$typeof:l}=s;s.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\profile\page.tsx#default`)},98958:(e,t,r)=>{"use strict";r.d(t,{Dx:()=>ea,VY:()=>er,aV:()=>et,dk:()=>es,fC:()=>Q,h_:()=>ee,jm:()=>Y,p8:()=>j,x8:()=>ei,xz:()=>X});var a=r(17577),s=r(82561),i=r(48051),l=r(93095),n=r(88957),o=r(52067),d=r(825),u=r(10441),c=r(83078),f=r(9815),m=r(45226),p=r(80699),x=r(58260),h=r(35664),y=r(34214),g=r(10326),v="Dialog",[b,j]=(0,l.b)(v),[w,N]=b(v),V=e=>{let{__scopeDialog:t,children:r,open:s,defaultOpen:i,onOpenChange:l,modal:d=!0}=e,u=a.useRef(null),c=a.useRef(null),[f,m]=(0,o.T)({prop:s,defaultProp:i??!1,onChange:l,caller:v});return(0,g.jsx)(w,{scope:t,triggerRef:u,contentRef:c,contentId:(0,n.M)(),titleId:(0,n.M)(),descriptionId:(0,n.M)(),open:f,onOpenChange:m,onOpenToggle:a.useCallback(()=>m(e=>!e),[m]),modal:d,children:r})};V.displayName=v;var k="DialogTrigger",_=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,l=N(k,r),n=(0,i.e)(t,l.triggerRef);return(0,g.jsx)(m.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":$(l.open),...a,ref:n,onClick:(0,s.M)(e.onClick,l.onOpenToggle)})});_.displayName=k;var D="DialogPortal",[A,C]=b(D,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:r,children:s,container:i}=e,l=N(D,t);return(0,g.jsx)(A,{scope:t,forceMount:r,children:a.Children.map(s,e=>(0,g.jsx)(f.z,{present:r||l.open,children:(0,g.jsx)(c.h,{asChild:!0,container:i,children:e})}))})};F.displayName=D;var S="DialogOverlay",R=a.forwardRef((e,t)=>{let r=C(S,e.__scopeDialog),{forceMount:a=r.forceMount,...s}=e,i=N(S,e.__scopeDialog);return i.modal?(0,g.jsx)(f.z,{present:a||i.open,children:(0,g.jsx)(M,{...s,ref:t})}):null});R.displayName=S;var E=(0,y.Z8)("DialogOverlay.RemoveScroll"),M=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(S,r);return(0,g.jsx)(x.Z,{as:E,allowPinchZoom:!0,shards:[s.contentRef],children:(0,g.jsx)(m.WV.div,{"data-state":$(s.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),P="DialogContent",O=a.forwardRef((e,t)=>{let r=C(P,e.__scopeDialog),{forceMount:a=r.forceMount,...s}=e,i=N(P,e.__scopeDialog);return(0,g.jsx)(f.z,{present:a||i.open,children:i.modal?(0,g.jsx)(U,{...s,ref:t}):(0,g.jsx)(Z,{...s,ref:t})})});O.displayName=P;var U=a.forwardRef((e,t)=>{let r=N(P,e.__scopeDialog),l=a.useRef(null),n=(0,i.e)(t,r.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,h.Ry)(e)},[]),(0,g.jsx)(I,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,s.M)(e.onFocusOutside,e=>e.preventDefault())})}),Z=a.forwardRef((e,t)=>{let r=N(P,e.__scopeDialog),s=a.useRef(!1),i=a.useRef(!1);return(0,g.jsx)(I,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||r.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;r.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),I=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:n,...o}=e,c=N(P,r),f=a.useRef(null),m=(0,i.e)(t,f);return(0,p.EW)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(u.M,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:l,onUnmountAutoFocus:n,children:(0,g.jsx)(d.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":$(c.open),...o,ref:m,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{titleId:c.titleId}),(0,g.jsx)(J,{contentRef:f,descriptionId:c.descriptionId})]})]})}),T="DialogTitle",q=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(T,r);return(0,g.jsx)(m.WV.h2,{id:s.titleId,...a,ref:t})});q.displayName=T;var z="DialogDescription",L=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(z,r);return(0,g.jsx)(m.WV.p,{id:s.descriptionId,...a,ref:t})});L.displayName=z;var B="DialogClose",W=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,i=N(B,r);return(0,g.jsx)(m.WV.button,{type:"button",...a,ref:t,onClick:(0,s.M)(e.onClick,()=>i.onOpenChange(!1))})});function $(e){return e?"open":"closed"}W.displayName=B;var H="DialogTitleWarning",[Y,G]=(0,l.k)(H,{contentName:P,titleName:T,docsSlug:"dialog"}),K=({titleId:e})=>{let t=G(H),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=G("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return a.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(s)},[s,e,t]),null},Q=V,X=_,ee=F,et=R,er=O,ea=q,es=L,ei=W},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var a=r(17577),s=r(45226),i=r(10326),l=a.forwardRef((e,t)=>(0,i.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},28407:(e,t,r)=>{"use strict";r.d(t,{VY:()=>S,aV:()=>C,fC:()=>A,xz:()=>F});var a=r(17577),s=r(82561),i=r(93095),l=r(15594),n=r(9815),o=r(45226),d=r(17124),u=r(52067),c=r(88957),f=r(10326),m="Tabs",[p,x]=(0,i.b)(m,[l.Pc]),h=(0,l.Pc)(),[y,g]=p(m),v=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:s,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:p="automatic",...x}=e,h=(0,d.gm)(n),[g,v]=(0,u.T)({prop:a,onChange:s,defaultProp:i??"",caller:m});return(0,f.jsx)(y,{scope:r,baseId:(0,c.M)(),value:g,onValueChange:v,orientation:l,dir:h,activationMode:p,children:(0,f.jsx)(o.WV.div,{dir:h,"data-orientation":l,...x,ref:t})})});v.displayName=m;var b="TabsList",j=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...s}=e,i=g(b,r),n=h(r);return(0,f.jsx)(l.fC,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:a,children:(0,f.jsx)(o.WV.div,{role:"tablist","aria-orientation":i.orientation,...s,ref:t})})});j.displayName=b;var w="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...n}=e,d=g(w,r),u=h(r),c=_(d.baseId,a),m=D(d.baseId,a),p=a===d.value;return(0,f.jsx)(l.ck,{asChild:!0,...u,focusable:!i,active:p,children:(0,f.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...n,ref:t,onMouseDown:(0,s.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,s.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,s.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||i||!e||d.onValueChange(a)})})})});N.displayName=w;var V="TabsContent",k=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:i,children:l,...d}=e,u=g(V,r),c=_(u.baseId,s),m=D(u.baseId,s),p=s===u.value,x=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(n.z,{present:i||p,children:({present:r})=>(0,f.jsx)(o.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&l})})});function _(e,t){return`${e}-trigger-${t}`}function D(e,t){return`${e}-content-${t}`}k.displayName=V;var A=v,C=j,F=N,S=k}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,9557,7410,86,7915,5999,2972,4433,6841],()=>r(74116));module.exports=a})();