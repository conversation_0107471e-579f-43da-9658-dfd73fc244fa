import { NextRequest, NextResponse } from 'next/server';
import { createHash } from 'crypto';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

// Tipos para o rate limiter avançado
interface RateLimitRecord {
  count: number;
  resetTime: number;
  blockedUntil?: number;
  consecutiveViolations?: number;
}

interface EnhancedRateLimiterOptions {
  // Período de tempo da janela (ms)
  windowMs: number;

  // Máximo de requisições na janela
  maxRequests: number;

  // Mensagem de erro
  message?: string;

  // Padrões de caminho para aplicar o limiter
  pathPatterns?: RegExp[];

  // Função para obter identificador único
  getIdentifier?: (req: NextRequest) => string;

  // Tempo de bloqueio base quando limite é excedido (ms)
  blockDurationMs?: number;

  // Fator de multiplicação para bloqueios subsequentes
  escalationFactor?: number;

  // Número máximo de entradas no store
  maxEntries?: number;

  // Se deve rejeitar silenciosamente (sem logs) acessos de clientes já bloqueados
  silentReject?: boolean;

  // Endpoints sensíveis que exigem regras mais rígidas
  sensitiveEndpoints?: RegExp[];

  // Limite para endpoints sensíveis (menor que maxRequests)
  sensitiveMaxRequests?: number;

  // ÁREA 7 SEGURANÇA - Rate Limiting Enhancement (18/06/2025)
  // Habilitar fingerprinting robusto para identificação mais precisa
  enableFingerprinting?: boolean;

  // Força do fingerprinting: low, medium, high
  fingerprintingStrength?: 'low' | 'medium' | 'high';
}

interface RateLimitResult {
  allowed: boolean;
  limited: boolean;
  remaining: number;
  resetTime: number;
  blockRemaining?: number;
  retryAfter?: number;
  message?: string;
  identifier: string;
  isSensitive?: boolean;
}

/**
 * Rate Limiter avançado com proteção contra abusos
 */
export class EnhancedRateLimiter {
  private store: Map<string, RateLimitRecord> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;
  private readonly message: string;
  private readonly blockDurationMs: number;
  private readonly escalationFactor: number;
  private readonly maxEntries: number;
  private readonly silentReject: boolean;
  private readonly pathPatterns: RegExp[];
  private readonly sensitiveEndpoints: RegExp[];
  private readonly sensitiveMaxRequests: number;
  private readonly getIdentifier: (req: NextRequest) => string;
  // ÁREA 7 SEGURANÇA - Rate Limiting Enhancement (18/06/2025)
  private readonly enableFingerprinting: boolean;
  private readonly fingerprintingStrength: 'low' | 'medium' | 'high';

  private lastCleanup: number = Date.now();

  constructor(options: EnhancedRateLimiterOptions) {
    this.windowMs = options.windowMs;
    this.maxRequests = options.maxRequests;
    this.message = options.message || 'Muitas requisições. Por favor, tente novamente mais tarde.';
    this.blockDurationMs = options.blockDurationMs || 10 * 60 * 1000; // 10 minutos padrão
    this.escalationFactor = options.escalationFactor || 2; // Dobrar tempo de bloqueio a cada violação
    this.maxEntries = options.maxEntries || 10000;
    this.silentReject = options.silentReject || false;
    this.pathPatterns = options.pathPatterns || [/.*/]; // Por padrão, aplica a todos os caminhos
    this.sensitiveEndpoints = options.sensitiveEndpoints || [];
    this.sensitiveMaxRequests = options.sensitiveMaxRequests || Math.floor(this.maxRequests / 2);

    // ÁREA 7 SEGURANÇA - Rate Limiting Enhancement (18/06/2025)
    this.enableFingerprinting = options.enableFingerprinting || false;
    this.fingerprintingStrength = options.fingerprintingStrength || 'medium';

    this.getIdentifier =
      options.getIdentifier ||
      ((req: NextRequest) => {
        if (this.enableFingerprinting) {
          return this.generateRobustFingerprint(req);
        }

        // Identificador padrão: endereço IP + user agent resumido
        const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
        const userAgent = req.headers.get('user-agent') || 'unknown';
        const userAgentHash = userAgent.substring(0, 20); // Usar apenas parte do UA

        return `${ip}:${userAgentHash}`;
      });

    // Iniciar limpeza periódica
    if (typeof setInterval !== 'undefined') {
      setInterval(() => this.cleanup(), 10 * 60 * 1000); // Limpar a cada 10 minutos
    }
  }

  /**
   * Gera fingerprint robusto para identificação mais precisa
   * ÁREA 7 SEGURANÇA - Rate Limiting Enhancement (18/06/2025)
   */
  private generateRobustFingerprint(req: NextRequest): string {
    const components: string[] = [];

    // 1. IP Address (sempre incluído)
    const ip = req.headers.get('x-forwarded-for') ||
               req.headers.get('x-real-ip') ||
               req.headers.get('cf-connecting-ip') ||
               'unknown';
    components.push(`ip:${ip}`);

    // 2. User Agent (força baixa: hash parcial, força alta: hash completo)
    const userAgent = req.headers.get('user-agent') || 'unknown';
    if (this.fingerprintingStrength === 'low') {
      components.push(`ua:${userAgent.substring(0, 30)}`);
    } else {
      const uaHash = createHash('sha256').update(userAgent).digest('hex').substring(0, 16);
      components.push(`ua:${uaHash}`);
    }

    // 3. Accept headers (força média e alta)
    if (this.fingerprintingStrength !== 'low') {
      const accept = req.headers.get('accept') || '';
      const acceptLang = req.headers.get('accept-language') || '';
      const acceptEnc = req.headers.get('accept-encoding') || '';

      const acceptHash = createHash('sha256')
        .update(`${accept}|${acceptLang}|${acceptEnc}`)
        .digest('hex')
        .substring(0, 12);
      components.push(`acc:${acceptHash}`);
    }

    // 4. Headers adicionais (apenas força alta)
    if (this.fingerprintingStrength === 'high') {
      const additionalHeaders = [
        'sec-ch-ua',
        'sec-ch-ua-mobile',
        'sec-ch-ua-platform',
        'sec-fetch-dest',
        'sec-fetch-mode',
        'sec-fetch-site',
        'dnt',
        'upgrade-insecure-requests'
      ];

      const headerValues = additionalHeaders
        .map(header => req.headers.get(header) || '')
        .join('|');

      if (headerValues) {
        const headerHash = createHash('sha256')
          .update(headerValues)
          .digest('hex')
          .substring(0, 8);
        components.push(`hdr:${headerHash}`);
      }
    }

    // 5. Timing-based component (força alta)
    if (this.fingerprintingStrength === 'high') {
      // Usar timestamp arredondado para criar um componente temporal
      const timeWindow = Math.floor(Date.now() / (5 * 60 * 1000)); // Janela de 5 minutos
      components.push(`tw:${timeWindow}`);
    }

    // Combinar todos os componentes
    const fingerprint = components.join('|');

    // Hash final para consistência de tamanho
    return createHash('sha256').update(fingerprint).digest('hex').substring(0, 24);
  }

  /**
   * Limpa entradas antigas para evitar vazamento de memória
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedEntries = 0;

    // Limpar apenas a cada 10 minutos
    if (now - this.lastCleanup < 10 * 60 * 1000) {
      return;
    }

    this.lastCleanup = now;

    for (const [key, record] of this.store.entries()) {
      // Remover entradas expiradas (resetTime passou e não está bloqueado)
      if (record.resetTime < now && (!record.blockedUntil || record.blockedUntil < now)) {
        this.store.delete(key);
        cleanedEntries++;
      }
    }

    if (cleanedEntries > 0 && !this.silentReject) {
      logger.debug(`[RateLimiter] Limpeza: ${cleanedEntries} entradas removidas`);
    }

    // Se o armazenamento estiver muito grande, limpe as entradas mais antigas
    if (this.store.size > this.maxEntries) {
      const entriesToRemove = Math.floor(this.store.size * 0.2); // Remover 20% das entradas
      const entries = Array.from(this.store.entries());

      // Ordenar pela data de expiração (mais antigas primeiro)
      entries.sort((a, b) => {
        // Garantir que a e b existem e têm o índice 1 definido
        if (!a || !a[1] || !b || !b[1]) return 0;
        return a[1].resetTime - b[1].resetTime;
      });

      // Remover entradas mais antigas
      for (let i = 0; i < entriesToRemove; i++) {
        if (i < entries.length && entries[i]) {
          const entry = entries[i];
          if (entry && entry[0]) {
            this.store.delete(entry[0]);
          }
        }
      }

      if (!this.silentReject) {
        logger.warn(
          `[RateLimiter] Armazenamento excedeu ${this.maxEntries} entradas, ${entriesToRemove} antigas removidas`
        );
      }
    }
  }

  /**
   * Verifica se uma requisição deve ser permitida ou limitada
   */
  public check(req: NextRequest): RateLimitResult {
    const path = req.nextUrl.pathname;
    const method = req.method;

    // Verificar se o caminho deve ser limitado
    const shouldLimit = this.pathPatterns.some(pattern => pattern.test(path));
    if (!shouldLimit) {
      return {
        allowed: true,
        limited: false,
        remaining: this.maxRequests,
        resetTime: Date.now() + this.windowMs,
        identifier: 'exempt',
      };
    }

    // Verificar se é um endpoint sensível
    const isSensitive = this.sensitiveEndpoints.some(pattern => pattern.test(path));
    const effectiveMaxRequests = isSensitive ? this.sensitiveMaxRequests : this.maxRequests;

    const identifier = this.getIdentifier(req);
    const now = Date.now();
    const key = `${identifier}:${path}`;

    // Obter registro existente ou criar novo
    let record = this.store.get(key);

    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.windowMs,
        consecutiveViolations: 0,
      };
    }

    // Verificar se está bloqueado
    if (record.blockedUntil && now < record.blockedUntil) {
      // Cliente está temporariamente bloqueado
      const blockRemaining = Math.ceil((record.blockedUntil - now) / 1000);

      // Registrar tentativas durante bloqueio (exceto se silentReject)
      if (!this.silentReject) {
        logger.warn(
          `[RateLimiter] Acesso bloqueado: ${key} ainda bloqueado por ${blockRemaining}s`
        );
      }

      return {
        allowed: false,
        limited: true,
        remaining: 0,
        resetTime: record.resetTime,
        blockRemaining,
        retryAfter: blockRemaining,
        message: this.message,
        identifier,
        isSensitive,
      };
    }

    // Reiniciar contador se a janela de tempo expirou
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + this.windowMs;

      // Reduzir contador de violações consecutivas se não houve bloqueio recente
      if (record.consecutiveViolations && record.consecutiveViolations > 0) {
        record.consecutiveViolations = Math.max(0, record.consecutiveViolations - 1);
      }
    }

    // Incrementar contador
    record.count++;

    // Verificar se excedeu o limite
    const allowed = record.count <= effectiveMaxRequests;

    // Calcular penalidade se limite foi excedido
    if (!allowed) {
      // Incrementar contador de violações consecutivas
      record.consecutiveViolations = (record.consecutiveViolations || 0) + 1;

      // Calcular duração do bloqueio com escalamento exponencial
      const blockDuration =
        this.blockDurationMs *
        Math.pow(
          this.escalationFactor,
          Math.min(5, record.consecutiveViolations - 1) // Limitar a 5 escalas para evitar tempos absurdos
        );

      // Aplicar bloqueio
      record.blockedUntil = now + blockDuration;

      // Registrar violação (exceto se silentReject)
      if (!this.silentReject) {
        const referer = req.headers.get('referer') || 'direct';
        const userAgent = req.headers.get('user-agent') || 'unknown';

        logger.warn(`[RateLimiter] Limite excedido: ${key} - ${method} ${path}
          - Violações consecutivas: ${record.consecutiveViolations}
          - Duração do bloqueio: ${Math.floor(blockDuration / 1000)}s
          - UA: ${userAgent.substring(0, 50)}
          - Origem: ${referer.substring(0, 50)}`);

        // Registrar possível tentativa de ataque ou abuso
        if (record.consecutiveViolations >= 3) {
          logger.error(`[SEGURANÇA] Possível abuso detectado: ${identifier}
            - Violações: ${record.consecutiveViolations}
            - Endpoint: ${method} ${path}
            - Bloqueado até: ${new Date(record.blockedUntil).toISOString()}`);
        }
      }
    }

    // Atualizar store
    this.store.set(key, record);

    // Sempre limpar a store quando ficar muito grande
    if (this.store.size > this.maxEntries) {
      this.cleanup();
    }

    // Calcular respostas
    const remaining = Math.max(0, effectiveMaxRequests - record.count);
    const retryAfter = !allowed ? Math.ceil((record.blockedUntil! - now) / 1000) : undefined;

    return {
      allowed,
      limited: !allowed,
      remaining,
      resetTime: record.resetTime,
      ...(retryAfter && { retryAfter, blockRemaining: retryAfter }),
      ...(!allowed && this.message && { message: this.message }),
      identifier,
      isSensitive,
    };
  }

  /**
   * Middleware para aplicar rate limiting em rotas Next.js
   */
  public middleware(req: NextRequest): NextResponse | null {
    const result = this.check(req);

    if (result.limited) {
      // Criar resposta de erro
      const headers = new Headers();
      headers.set('Content-Type', 'application/json');
      headers.set(
        'X-RateLimit-Limit',
        String(result.isSensitive ? this.sensitiveMaxRequests : this.maxRequests)
      );
      headers.set('X-RateLimit-Remaining', String(result.remaining));
      headers.set('X-RateLimit-Reset', String(Math.floor(result.resetTime / 1000)));

      if (result.retryAfter) {
        headers.set('Retry-After', String(result.retryAfter));
      }

      return new NextResponse(
        JSON.stringify({
          error: {
            status: 429,
            message: result.message || this.message,
          },
        }),
        {
          status: 429,
          headers,
        }
      );
    }

    return null;
  }
}

/**
 * Cria um conjunto de rate limiters pré-configurados para diferentes cenários
 */
export function createRateLimiters() {
  const limiters = {
    // Limiter para APIs gerais
    api: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 100 : 500,
      pathPatterns: [/^\/api\/.*/i],
      blockDurationMs: 5 * 60 * 1000, // 5 minutos
      message: 'Muitas requisições. Por favor, tente novamente em alguns minutos.',
    }),

    // Limiter específico para operações Excel (mais permissivo)
    excel: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 200 : 1000,
      pathPatterns: [/^\/api\/excel\/.*/i],
      blockDurationMs: 3 * 60 * 1000, // 3 minutos
      message: 'Muitas operações Excel em um curto período. Por favor, aguarde alguns minutos.',
    }),

    // Limiter restritivo para operações sensíveis (pagamentos, autenticação)
    // ÁREA 7 SEGURANÇA - Rate Limiting Enhancement (18/06/2025)
    secure: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 15 : 100, // Mais restritivo em produção
      pathPatterns: [
        /^\/api\/auth\/.*/i,
        /^\/api\/payment\/.*/i,
        /^\/api\/subscription\/.*/i,
        /^\/api\/webhooks\/.*/i,
        /^\/api\/admin\/.*/i,
      ],
      sensitiveEndpoints: [
        /^\/api\/auth\/signin/i,
        /^\/api\/payment\/create-checkout/i,
        /^\/api\/admin\/.*/i,
        /^\/api\/workbooks\/delete/i, // Operações destrutivas
        /^\/api\/user\/delete/i,
      ],
      sensitiveMaxRequests: ENV.IS_PRODUCTION ? 5 : 50, // Muito mais restritivo
      blockDurationMs: 30 * 60 * 1000, // 30 minutos (aumentado)
      escalationFactor: 5, // Escalamento mais agressivo
      message: 'Muitas tentativas. Por razões de segurança, aguarde antes de tentar novamente.',
      // Fingerprinting robusto habilitado
      enableFingerprinting: true,
      fingerprintingStrength: 'high',
    }),

    // Limiter específico para API de IA/chat (com cotas)
    ai: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 30 : 200,
      pathPatterns: [/^\/api\/chat\/.*/i, /^\/api\/ai\/.*/i],
      blockDurationMs: 2 * 60 * 1000, // 2 minutos
      message: 'Cota de solicitações de IA excedida. Por favor, aguarde um momento.',
    }),
  };

  return limiters;
}
