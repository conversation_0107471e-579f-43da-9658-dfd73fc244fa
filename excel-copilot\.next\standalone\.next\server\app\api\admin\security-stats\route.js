"use strict";(()=>{var e={};e.id=5513,e.ids=[5513],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},84016:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>h,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>E,staticGenerationAsyncStorage:()=>v});var o={};t.r(o),t.d(o,{DELETE:()=>m,GET:()=>p,dynamic:()=>d});var a=t(49303),n=t(88716),s=t(60670),i=t(87070),c=t(20330);t(43895),t(81222);async function u(e,r){if(console.error(`🔴 ANOMALIA CR\xcdTICA: ${r.description}`),e.userId)try{let{prisma:r}=await Promise.resolve().then(t.bind(t,63841));await r.user.update({where:{id:e.userId},data:{isSuspicious:!0}})}catch(e){console.error("Erro ao marcar usu\xe1rio como suspeito:",e)}}async function l(e,r){console.warn(`🟡 ANOMALIA ALTA SEVERIDADE: ${r.description}`)}t(63841);let d="force-dynamic";async function p(e){try{if(!await (0,c.getToken)({req:e,secret:process.env.AUTH_NEXTAUTH_SECRET||""}))return i.NextResponse.json({error:"N\xe3o autenticado"},{status:401});return i.NextResponse.json({error:"Endpoint dispon\xedvel apenas em desenvolvimento"},{status:403})}catch(e){return console.error("Erro ao obter estat\xedsticas de seguran\xe7a:",e),i.NextResponse.json({error:"Erro interno do servidor",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}async function m(e){try{if(!await (0,c.getToken)({req:e,secret:process.env.AUTH_NEXTAUTH_SECRET||""}))return i.NextResponse.json({error:"N\xe3o autenticado"},{status:401});return i.NextResponse.json({error:"Opera\xe7\xe3o dispon\xedvel apenas em desenvolvimento"},{status:403})}catch(e){return console.error("Erro ao limpar dados de seguran\xe7a:",e),i.NextResponse.json({error:"Erro interno do servidor",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/security-stats/route",pathname:"/api/admin/security-stats",filename:"route",bundlePath:"app/api/admin/security-stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\security-stats\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:v,serverHooks:E}=f,x="/api/admin/security-stats/route";function h(){return(0,s.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:v})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>l});var a=t(99557),n=t.n(a);function s(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:s(e),extractedMetadata:e}:{normalizedError:s(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err}}};try{let e=u.production;o=n()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=n()({level:"info",formatters:{level:e=>({level:e})}})}let l={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);o.warn(t,e)}else o.warn(c(r)||{},e)},error:(e,r,t)=>{let{normalizedError:a,extractedMetadata:n}=i(r),s={...t||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.error(s,e)},fatal:(e,r,t)=>{let{normalizedError:a,extractedMetadata:n}=i(r),s={...t||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.fatal(s,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=i(t);r.warn(o,e)}else r.warn(c(t)||{},e)},error:(e,t,o)=>{let{normalizedError:a,extractedMetadata:n}=i(t),s={...o||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.error(s,e)},fatal:(e,t,o)=>{let{normalizedError:a,extractedMetadata:n}=i(t),s={...o||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.fatal(s,e)}}},child:function(e){return this.createChild(e)}}},81222:(e,r,t)=>{t.d(r,{j2:()=>s,lv:()=>n}),t(87070);let o=new Map,a={maxAttempts:5};function n(e,r){let t=Buffer.from(`${e}-${r||"unknown"}`).toString("base64").slice(0,16),a=`oauth_limit:${t}`;return o.delete(a)}function s(){let e=Date.now(),r=0,t=0;for(let n of o.values())e<=n.resetTime&&(t++,n.count>a.maxAttempts&&r++);return{totalEntries:o.size,blockedIPs:r,activeEntries:t}}},63841:(e,r,t)=>{t.d(r,{P:()=>c,prisma:()=>i});var o=t(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},n={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},s=[],i=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function c(){return{...n,activeConnections:Math.min(Math.floor(5*Math.random())+1,n.maxPoolSize),poolSize:n.poolSize}}async function u(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{n.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),n.averageQueryTime=s.reduce((e,r)=>e+r,0)/s.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{n.failedQueries++,n.connectionFailures++,n.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,330],()=>t(84016));module.exports=o})();