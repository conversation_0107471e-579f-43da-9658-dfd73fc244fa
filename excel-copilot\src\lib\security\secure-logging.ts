/**
 * Secure Logging - ÁREA 7 SEGURANÇA (18/06/2025)
 * Sistema de sanitização de logs para prevenir vazamento de dados sensíveis
 */

import { logger } from '../logger';

// Padrões de dados sensíveis que devem ser sanitizados
const SENSITIVE_PATTERNS = [
  // Tokens e chaves
  {
    pattern: /sk_live_[a-zA-Z0-9]{24,}/gi,
    replacement: 'sk_live_[REDACTED]',
    description: 'Stripe Live Secret Key'
  },
  {
    pattern: /sk_test_[a-zA-Z0-9]{24,}/gi,
    replacement: 'sk_test_[REDACTED]',
    description: 'Stripe Test Secret Key'
  },
  {
    pattern: /pk_live_[a-zA-Z0-9]{24,}/gi,
    replacement: 'pk_live_[REDACTED]',
    description: 'Stripe Live Publishable Key'
  },
  {
    pattern: /pk_test_[a-zA-Z0-9]{24,}/gi,
    replacement: 'pk_test_[REDACTED]',
    description: 'Stripe Test Publishable Key'
  },
  
  // Tokens de autenticação
  {
    pattern: /Bearer\s+[a-zA-Z0-9\-._~+/]+=*/gi,
    replacement: 'Bearer [REDACTED]',
    description: 'Bearer Token'
  },
  {
    pattern: /token["\s]*[:=]["\s]*[a-zA-Z0-9\-._~+/]{20,}/gi,
    replacement: 'token: "[REDACTED]"',
    description: 'Generic Token'
  },
  
  // Senhas
  {
    pattern: /password["\s]*[:=]["\s]*[^"\s]{6,}/gi,
    replacement: 'password: "[REDACTED]"',
    description: 'Password'
  },
  {
    pattern: /passwd["\s]*[:=]["\s]*[^"\s]{6,}/gi,
    replacement: 'passwd: "[REDACTED]"',
    description: 'Password (passwd)'
  },
  
  // Chaves de API
  {
    pattern: /api[_-]?key["\s]*[:=]["\s]*[a-zA-Z0-9\-._~+/]{16,}/gi,
    replacement: 'api_key: "[REDACTED]"',
    description: 'API Key'
  },
  {
    pattern: /secret[_-]?key["\s]*[:=]["\s]*[a-zA-Z0-9\-._~+/]{16,}/gi,
    replacement: 'secret_key: "[REDACTED]"',
    description: 'Secret Key'
  },
  
  // URLs de banco de dados
  {
    pattern: /postgresql:\/\/[^:\s]+:[^@\s]+@[^\s]+/gi,
    replacement: 'postgresql://[USER]:[PASSWORD]@[HOST]/[DB]',
    description: 'PostgreSQL URL'
  },
  {
    pattern: /mysql:\/\/[^:\s]+:[^@\s]+@[^\s]+/gi,
    replacement: 'mysql://[USER]:[PASSWORD]@[HOST]/[DB]',
    description: 'MySQL URL'
  },
  
  // Emails (parcial)
  {
    pattern: /([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi,
    replacement: (match: string) => {
      const [local, domain] = match.split('@');
      if (!local || !domain) return match;
      const maskedLocal = local.length > 2 ?
        local.substring(0, 2) + '*'.repeat(local.length - 2) :
        '*'.repeat(local.length);
      return `${maskedLocal}@${domain}`;
    },
    description: 'Email Address'
  },
  
  // IPs privados (manter apenas classe)
  {
    pattern: /\b192\.168\.\d{1,3}\.\d{1,3}\b/gi,
    replacement: '192.168.x.x',
    description: 'Private IP (192.168.x.x)'
  },
  {
    pattern: /\b10\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/gi,
    replacement: '10.x.x.x',
    description: 'Private IP (10.x.x.x)'
  },
  {
    pattern: /\b172\.(1[6-9]|2[0-9]|3[01])\.\d{1,3}\.\d{1,3}\b/gi,
    replacement: '172.x.x.x',
    description: 'Private IP (172.x.x.x)'
  },
  
  // Números de cartão de crédito
  {
    pattern: /\b(?:\d{4}[-\s]?){3}\d{4}\b/gi,
    replacement: '**** **** **** ****',
    description: 'Credit Card Number'
  },
  
  // CPF/CNPJ
  {
    pattern: /\b\d{3}\.\d{3}\.\d{3}-\d{2}\b/gi,
    replacement: '***.***.***-**',
    description: 'CPF'
  },
  {
    pattern: /\b\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}\b/gi,
    replacement: '**.***.***/**-**',
    description: 'CNPJ'
  },
];

/**
 * Sanitiza dados sensíveis de uma string
 */
export function sanitizeLogData(data: any): any {
  if (typeof data === 'string') {
    return sanitizeString(data);
  }
  
  if (typeof data === 'object' && data !== null) {
    if (Array.isArray(data)) {
      return data.map(item => sanitizeLogData(item));
    }
    
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Sanitizar chave se for sensível
      const sanitizedKey = sanitizeString(key);
      
      // Sanitizar valor recursivamente
      sanitized[sanitizedKey] = sanitizeLogData(value);
    }
    return sanitized;
  }
  
  return data;
}

/**
 * Sanitiza uma string removendo dados sensíveis
 */
function sanitizeString(str: string): string {
  if (!str || typeof str !== 'string') {
    return str;
  }
  
  let sanitized = str;
  
  for (const { pattern, replacement } of SENSITIVE_PATTERNS) {
    if (typeof replacement === 'function') {
      sanitized = sanitized.replace(pattern, replacement);
    } else {
      sanitized = sanitized.replace(pattern, replacement);
    }
  }
  
  return sanitized;
}

/**
 * Logger seguro que sanitiza automaticamente os dados
 */
export const secureLogger = {
  debug: (message: string, data?: any) => {
    logger.debug(sanitizeString(message), data ? sanitizeLogData(data) : undefined);
  },
  
  info: (message: string, data?: any) => {
    logger.info(sanitizeString(message), data ? sanitizeLogData(data) : undefined);
  },
  
  warn: (message: string, data?: any) => {
    logger.warn(sanitizeString(message), data ? sanitizeLogData(data) : undefined);
  },
  
  error: (message: string, error?: any, data?: any) => {
    const sanitizedMessage = sanitizeString(message);
    const sanitizedError = error ? sanitizeLogData(error) : undefined;
    const sanitizedData = data ? sanitizeLogData(data) : undefined;
    
    logger.error(sanitizedMessage, sanitizedError, sanitizedData);
  },
};

/**
 * Middleware para sanitizar logs automaticamente
 */
export function withSecureLogging<T extends (...args: any[]) => any>(
  fn: T,
  context: string
): T {
  return ((...args: any[]) => {
    try {
      const sanitizedArgs = args.map(arg => sanitizeLogData(arg));
      secureLogger.debug(`${context}: Executando função`, { args: sanitizedArgs });
      
      const result = fn(...args);
      
      if (result instanceof Promise) {
        return result.catch((error: any) => {
          secureLogger.error(`${context}: Erro na execução`, error, { args: sanitizedArgs });
          throw error;
        });
      }
      
      return result;
    } catch (error) {
      secureLogger.error(`${context}: Erro na execução`, error, { args: sanitizeLogData(args) });
      throw error;
    }
  }) as T;
}

/**
 * Verifica se uma string contém dados sensíveis
 */
export function containsSensitiveData(str: string): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }
  
  return SENSITIVE_PATTERNS.some(({ pattern }) => pattern.test(str));
}

/**
 * Obtém estatísticas de sanitização
 */
export function getSanitizationStats(data: any): {
  totalFields: number;
  sanitizedFields: number;
  sensitivePatterns: string[];
} {
  const stats = {
    totalFields: 0,
    sanitizedFields: 0,
    sensitivePatterns: [] as string[],
  };
  
  function analyzeValue(value: any, key?: string) {
    stats.totalFields++;
    
    if (typeof value === 'string') {
      for (const { pattern, description } of SENSITIVE_PATTERNS) {
        if (pattern.test(value)) {
          stats.sanitizedFields++;
          if (!stats.sensitivePatterns.includes(description)) {
            stats.sensitivePatterns.push(description);
          }
          break;
        }
      }
    } else if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(item => analyzeValue(item));
      } else {
        Object.entries(value).forEach(([k, v]) => analyzeValue(v, k));
      }
    }
  }
  
  analyzeValue(data);
  return stats;
}

// Exportar padrões para testes
export { SENSITIVE_PATTERNS };
