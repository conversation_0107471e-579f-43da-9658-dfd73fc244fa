"use strict";(()=>{var e={};e.id=4958,e.ids=[4958],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},20519:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>v});var a={};r.r(a),r.d(a,{GET:()=>f,dynamic:()=>h,runtime:()=>p});var s=r(49303),i=r(88716),o=r(60670),n=r(87070),l=r(43895),c=r(63841),u=r(4579);let d="phase-production-build"===process.env.NEXT_PHASE||"true"===process.env.NEXT_STATIC_EXPORT,h="force-dynamic",p="nodejs";async function f(){if(d)return l.kg.info("Pulando verifica\xe7\xe3o de DB durante build est\xe1tico"),n.NextResponse.json({status:"skipped",message:"Verifica\xe7\xe3o de sa\xfade do banco ignorada durante build est\xe1tico"});try{let e=Date.now();await c.prisma.$queryRaw`SELECT 1`;let t=Date.now()-e,r=(0,c.P)(),a=(0,u.UZ)();return n.NextResponse.json({status:"healthy",responseTime:`${t}ms`,metrics:{activeConnections:r.activeConnections,totalQueries:r.totalQueries,failedQueries:r.failedQueries,averageQueryTime:`${r.averageQueryTime.toFixed(2)}ms`,connectionFailures:r.connectionFailures,lastConnectionFailure:r.lastConnectionFailure,cacheSize:a.size}})}catch(t){let e=t instanceof Error?t.message:"Erro desconhecido";return l.kg.error("Falha na verifica\xe7\xe3o de sa\xfade do banco de dados:",t),n.NextResponse.json({status:"unhealthy",error:e,timestamp:new Date().toISOString()},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/health/db/route",pathname:"/api/health/db",filename:"route",bundlePath:"app/api/health/db/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\db\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:v,serverHooks:y}=m,x="/api/health/db/route";function b(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:v})}},43895:(e,t,r)=>{let a;r.d(t,{kg:()=>u});var s=r(99557),i=r.n(s);function o(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function n(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],r={};return Object.keys(e).forEach(a=>{t.includes(a)||(r[a]=e[a])}),{normalizedError:e,extractedMetadata:r}}return"object"==typeof e&&null!==e?{normalizedError:o(e),extractedMetadata:e}:{normalizedError:o(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:i().stdSerializers.err,error:i().stdSerializers.err}}};try{let e=c.production;a=i()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=i()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,t)=>{a.trace(t||{},e)},debug:(e,t)=>{a.debug(t||{},e)},info:(e,t)=>{a.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:r}=n(t);a.warn(r,e)}else a.warn(l(t)||{},e)},error:(e,t,r)=>{let{normalizedError:s,extractedMetadata:i}=n(t),o={...r||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.error(o,e)},fatal:(e,t,r)=>{let{normalizedError:s,extractedMetadata:i}=n(t),o={...r||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.fatal(o,e)},createChild:e=>{let t=a.child(e);return{trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:a}=n(r);t.warn(a,e)}else t.warn(l(r)||{},e)},error:(e,r,a)=>{let{normalizedError:s,extractedMetadata:i}=n(r),o={...a||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.error(o,e)},fatal:(e,r,a)=>{let{normalizedError:s,extractedMetadata:i}=n(r),o={...a||{},...i,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.fatal(o,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,t,r)=>{r.d(t,{P:()=>l,prisma:()=>n});var a=r(53524);let s={info:(e,...t)=>{},error:(e,...t)=>{console.error(`[DB ERROR] ${e}`,...t)},warn:(e,...t)=>{console.warn(`[DB WARNING] ${e}`,...t)}},i={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},o=[],n=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...i,activeConnections:Math.min(Math.floor(5*Math.random())+1,i.maxPoolSize),poolSize:i.poolSize}}async function c(){try{await n.$disconnect(),s.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){s.error("Erro ao desconectar do banco de dados",e)}}n.$on("query",e=>{i.totalQueries++,e.duration&&(o.push(e.duration),o.length>100&&o.shift(),i.averageQueryTime=o.reduce((e,t)=>e+t,0)/o.length),e.duration&&e.duration>500&&s.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),n.$on("error",e=>{i.failedQueries++,i.connectionFailures++,i.lastConnectionFailure=new Date().toISOString(),s.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})},4579:(e,t,r)=>{r.d(t,{UZ:()=>u,e5:()=>c,jw:()=>l});var a=r(52972),s=r(43895);let i=a.Vi.CACHE?.DEFAULT_TTL||60;class o{constructor(){this.cache=new Map,this.checkInterval=null,this.stats={hits:0,misses:0,sets:0,evictions:0},this.checkInterval=setInterval(()=>this.cleanup(),3e4)}set(e,t,r=i){this.cache.size>=1e3&&this.evictOldest(),this.cache.set(e,{value:t,expiry:Date.now()+1e3*r}),this.stats.sets++}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),this.stats.misses++,null):(this.stats.hits++,t.value):(this.stats.misses++,null)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}invalidateByPrefix(e){let t=0;for(let r of this.cache.keys())r.startsWith(e)&&(this.cache.delete(r),t++);return t}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e>r.expiry&&this.cache.delete(t)}evictOldest(){let e=null,t=Date.now();for(let[r,a]of this.cache.entries())a.expiry<t&&(e=r,t=a.expiry);e&&(this.cache.delete(e),this.stats.evictions++)}shutdown(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null,s.kg.debug("Cache de consultas desligado")),this.clear()}getStats(){return{...this.stats,size:this.cache.size,maxSize:1e3,hitRatio:this.stats.hits/(this.stats.hits+this.stats.misses)||0}}}let n=new o;async function l(e,t,r={}){if(!a.Vi.IS_PRODUCTION)return e();let s=t.map(e=>{if(null==e)return"null";if("object"==typeof e)try{return JSON.stringify(e)}catch{}return String(e)}).join(":"),i=n.get(s);if(null!==i)return i;let o=await e();return n.set(s,o,r.ttl),o}function c(e){return n.invalidateByPrefix(e)}function u(){return n.getStats()}"undefined"!=typeof process&&process.on("beforeExit",()=>{n.shutdown(),s.kg.debug("Cache de queries desligado")})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,9557,7410,2972],()=>r(20519));module.exports=a})();