/**
 * Universal Zod Validation - ÁREA 7 SEGURANÇA (18/06/2025)
 * Sistema de validação universal para todos os endpoints de API
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { logger } from '../logger';
import { secureLogger } from './secure-logging';

// Schemas base para validação comum
const BaseSchemas = {
  // ID genérico
  id: z.string().min(1, 'ID é obrigatório').max(100, 'ID muito longo'),
  
  // UUID
  uuid: z.string().uuid('UUID inválido'),
  
  // Email
  email: z.string().email('Email inválido').max(255, 'Email muito longo'),
  
  // URL
  url: z.string().url('URL inválida').max(2048, 'URL muito longa'),
  
  // Texto seguro (sem HTML/scripts)
  safeText: z.string()
    .min(1)
    .max(10000, 'Texto muito longo')
    .refine(
      (val) => !/<script|javascript:|data:|vbscript:/i.test(val),
      'Conteúdo potencialmente perigoso detectado'
    ),
  
  // Nome de arquivo seguro
  filename: z.string()
    .min(1, 'Nome do arquivo é obrigatório')
    .max(255, 'Nome do arquivo muito longo')
    .refine(
      (val) => /^[a-zA-Z0-9._-]+$/.test(val),
      'Nome do arquivo contém caracteres inválidos'
    ),
  
  // Paginação
  pagination: z.object({
    page: z.number().int().min(1).default(1),
    limit: z.number().int().min(1).max(100).default(20),
  }),
  
  // Filtros de data
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }).refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return new Date(data.startDate) <= new Date(data.endDate);
      }
      return true;
    },
    'Data de início deve ser anterior à data de fim'
  ),
};

// Schemas específicos para workbooks
const WorkbookSchemas = {
  create: z.object({
    name: z.string().min(1, 'Nome é obrigatório').max(255, 'Nome muito longo'),
    description: z.string().max(1000, 'Descrição muito longa').optional(),
    isPublic: z.boolean().default(false),
    tags: z.array(z.string().max(50, 'Tag muito longa')).max(10, 'Muitas tags').optional(),
  }),

  update: z.object({
    id: BaseSchemas.uuid,
    name: z.string().min(1, 'Nome é obrigatório').max(255, 'Nome muito longo').optional(),
    description: z.string().max(1000, 'Descrição muito longa').optional(),
    isPublic: z.boolean().optional(),
    tags: z.array(z.string().max(50, 'Tag muito longa')).max(10, 'Muitas tags').optional(),
  }),
  
  delete: z.object({
    id: BaseSchemas.uuid,
    confirmDelete: z.literal(true, { errorMap: () => ({ message: 'Confirmação de exclusão é obrigatória' }) }),
  }),
  
  share: z.object({
    id: BaseSchemas.uuid,
    email: BaseSchemas.email,
    permission: z.enum(['read', 'write', 'admin'], { errorMap: () => ({ message: 'Permissão inválida' }) }),
  }),
};

// Schemas para chat/AI
const ChatSchemas = {
  message: z.object({
    content: z.string().min(1, 'Mensagem é obrigatória').max(5000, 'Mensagem muito longa'),
    workbookId: BaseSchemas.uuid.optional(),
    context: z.object({
      selectedCells: z.array(z.string()).max(1000, 'Muitas células selecionadas').optional(),
      sheetName: z.string().max(100, 'Nome da planilha muito longo').optional(),
    }).optional(),
  }),

  feedback: z.object({
    messageId: BaseSchemas.uuid,
    rating: z.number().int().min(1).max(5),
    comment: z.string().max(500, 'Comentário muito longo').optional(),
  }),
};

// Schemas para autenticação
const AuthSchemas = {
  signin: z.object({
    email: BaseSchemas.email,
    password: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres').max(128, 'Senha muito longa'),
    rememberMe: z.boolean().default(false),
  }),
  
  signup: z.object({
    email: BaseSchemas.email,
    password: z.string()
      .min(8, 'Senha deve ter pelo menos 8 caracteres')
      .max(128, 'Senha muito longa')
      .refine(
        (val) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(val),
        'Senha deve conter pelo menos uma letra minúscula, uma maiúscula e um número'
      ),
    name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome muito longo'),
    acceptTerms: z.literal(true, { errorMap: () => ({ message: 'Você deve aceitar os termos de uso' }) }),
  }),
  
  resetPassword: z.object({
    email: BaseSchemas.email,
  }),
};

// Mapeamento de endpoints para schemas
const ENDPOINT_SCHEMAS: Record<string, z.ZodSchema> = {
  // Workbooks
  'POST /api/workbooks': WorkbookSchemas.create,
  'PUT /api/workbooks': WorkbookSchemas.update,
  'DELETE /api/workbooks': WorkbookSchemas.delete,
  'POST /api/workbooks/share': WorkbookSchemas.share,
  
  // Chat/AI
  'POST /api/chat': ChatSchemas.message,
  'POST /api/chat/feedback': ChatSchemas.feedback,
  
  // Auth
  'POST /api/auth/signin': AuthSchemas.signin,
  'POST /api/auth/signup': AuthSchemas.signup,
  'POST /api/auth/reset-password': AuthSchemas.resetPassword,
};

/**
 * Obtém o schema de validação para um endpoint
 */
export function getSchemaForEndpoint(method: string, pathname: string): z.ZodSchema | null {
  const key = `${method} ${pathname}`;
  
  // Busca exata primeiro
  if (ENDPOINT_SCHEMAS[key]) {
    return ENDPOINT_SCHEMAS[key];
  }
  
  // Busca por padrões
  for (const [pattern, schema] of Object.entries(ENDPOINT_SCHEMAS)) {
    const [patternMethod, patternPath] = pattern.split(' ');
    
    if (method === patternMethod) {
      // Converter padrão para regex
      if (!patternPath) continue;

      const regexPattern = patternPath
        .replace(/\[.*?\]/g, '[^/]+') // [id] -> [^/]+
        .replace(/\*/g, '.*'); // * -> .*
      
      const regex = new RegExp(`^${regexPattern}$`);
      if (regex.test(pathname)) {
        return schema;
      }
    }
  }
  
  return null;
}

/**
 * Middleware universal de validação Zod
 */
export function withUniversalValidation(
  handler: (req: NextRequest, validatedData?: any) => Promise<NextResponse> | NextResponse
) {
  return async function (req: NextRequest): Promise<NextResponse> {
    const method = req.method;
    const pathname = req.nextUrl.pathname;
    
    // Métodos seguros não precisam de validação de body
    if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
      return handler(req);
    }
    
    // Obter schema para o endpoint
    const schema = getSchemaForEndpoint(method, pathname);
    
    if (!schema) {
      // Sem schema definido, prosseguir sem validação
      secureLogger.debug(`Validação: Nenhum schema definido para ${method} ${pathname}`);
      return handler(req);
    }
    
    try {
      // Extrair dados do body
      let body: any;
      const contentType = req.headers.get('content-type') || '';
      
      if (contentType.includes('application/json')) {
        body = await req.json();
      } else if (contentType.includes('application/x-www-form-urlencoded')) {
        const formData = await req.formData();
        body = Object.fromEntries(formData.entries());
      } else {
        // Tipo de conteúdo não suportado para validação
        secureLogger.warn(`Validação: Tipo de conteúdo não suportado: ${contentType}`);
        return NextResponse.json(
          {
            error: 'Tipo de conteúdo não suportado',
            code: 'UNSUPPORTED_CONTENT_TYPE'
          },
          { status: 400 }
        );
      }
      
      // Validar com Zod
      const validationResult = schema.safeParse(body);
      
      if (!validationResult.success) {
        secureLogger.warn(`Validação falhou para ${method} ${pathname}`, {
          errors: validationResult.error.format(),
          receivedData: body
        });
        
        return NextResponse.json(
          {
            error: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: validationResult.error.format()
          },
          { status: 400 }
        );
      }
      
      // Dados válidos, prosseguir com handler
      secureLogger.debug(`Validação bem-sucedida para ${method} ${pathname}`);
      
      // Criar nova requisição com dados validados
      const validatedReq = new NextRequest(req.url, {
        method: req.method,
        headers: req.headers,
        body: JSON.stringify(validationResult.data),
      });
      
      return handler(validatedReq, validationResult.data);
      
    } catch (error) {
      secureLogger.error(`Erro durante validação para ${method} ${pathname}`, error);
      
      return NextResponse.json(
        {
          error: 'Erro interno durante validação',
          code: 'VALIDATION_INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Registra um novo schema para um endpoint
 */
export function registerEndpointSchema(method: string, pathname: string, schema: z.ZodSchema): void {
  const key = `${method} ${pathname}`;
  ENDPOINT_SCHEMAS[key] = schema;
  logger.debug(`Schema registrado para ${key}`);
}

/**
 * Obtém estatísticas de validação
 */
export function getValidationStats(): {
  totalEndpoints: number;
  validatedEndpoints: number;
  coverage: number;
} {
  const totalEndpoints = Object.keys(ENDPOINT_SCHEMAS).length;
  const validatedEndpoints = Object.values(ENDPOINT_SCHEMAS).filter(Boolean).length;
  const coverage = totalEndpoints > 0 ? (validatedEndpoints / totalEndpoints) * 100 : 0;
  
  return {
    totalEndpoints,
    validatedEndpoints,
    coverage: Math.round(coverage * 100) / 100
  };
}

// Exportar schemas para uso externo
export { BaseSchemas, WorkbookSchemas, ChatSchemas, AuthSchemas };
