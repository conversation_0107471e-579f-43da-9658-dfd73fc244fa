"use strict";(()=>{var e={};e.id=4198,e.ids=[4198],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},22750:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>w,routeModule:()=>k,serverHooks:()=>b,staticGenerationAsyncStorage:()=>x});var o={};t.r(o),t.d(o,{GET:()=>h,POST:()=>f,dynamic:()=>c,runtime:()=>m});var a=t(49303),s=t(88716),i=t(60670),n=t(87070),u=t(52972),d=t(95456),p=t(43895),l=t(63841);let c="force-dynamic",m="nodejs";async function h(e){try{let e=await (0,d.rc)();if(!e){if(u.Vi.IS_DEVELOPMENT&&u.Vi.FEATURES?.SKIP_AUTH_PROVIDERS)return p.kg.warn("Permitindo acesso sem autentica\xe7\xe3o em modo de desenvolvimento"),n.NextResponse.json({workbooks:[{id:"shared-1",name:"Planilha Compartilhada 1",createdAt:new Date,updatedAt:new Date,sharedBy:{name:"Usu\xe1rio Exemplo",email:"<EMAIL>"},sheets:[{id:"sheet-1"}]},{id:"shared-2",name:"Planilha Compartilhada 2",createdAt:new Date,updatedAt:new Date,sharedBy:{name:"Outro Usu\xe1rio",email:"<EMAIL>"},sheets:[{id:"sheet-2"}]}]});return n.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401})}let r=(await l.prisma.workbookShare.findMany({where:{sharedWithUserId:e},include:{workbook:{include:{sheets:{select:{id:!0}}}},sharedByUser:{select:{id:!0,name:!0,email:!0,image:!0}}},orderBy:{createdAt:"desc"}})).map(e=>({id:e.workbook.id,name:e.workbook.name,createdAt:e.workbook.createdAt,updatedAt:e.workbook.updatedAt,sheets:e.workbook.sheets,sharedBy:{id:e.sharedByUser.id,name:e.sharedByUser.name,email:e.sharedByUser.email,image:e.sharedByUser.image},sharedAt:e.createdAt,permissionLevel:e.permissionLevel}));return n.NextResponse.json({workbooks:r})}catch(e){return p.kg.error("Erro ao listar workbooks compartilhados:",e),n.NextResponse.json({error:"Erro ao buscar planilhas compartilhadas",details:e instanceof Error?e.message:String(e)},{status:500})}}async function f(e){try{let r=await (0,d.rc)();if(!r)return n.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let{workbookId:t,userEmail:o,permissionLevel:a="READ"}=await e.json();if(!t||!o)return n.NextResponse.json({error:"Dados inv\xe1lidos",details:"ID da planilha e email do usu\xe1rio s\xe3o obrigat\xf3rios"},{status:400});if(!await l.prisma.workbook.findFirst({where:{id:t,userId:r}}))return n.NextResponse.json({error:"Planilha n\xe3o encontrada ou sem permiss\xe3o",details:"A planilha n\xe3o existe ou voc\xea n\xe3o tem permiss\xe3o para compartilh\xe1-la"},{status:404});let s=await l.prisma.user.findUnique({where:{email:o},select:{id:!0,email:!0}});if(!s)return n.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado",details:"N\xe3o foi encontrado um usu\xe1rio com este email"},{status:404});let i=await l.prisma.workbookShare.findUnique({where:{workbookId_sharedWithUserId:{workbookId:t,sharedWithUserId:s.id}}});if(i){let e=await l.prisma.workbookShare.update({where:{id:i.id},data:{permissionLevel:a,updatedAt:new Date}});return n.NextResponse.json({message:"Permiss\xe3o de compartilhamento atualizada",share:e})}let u=await l.prisma.workbookShare.create({data:{workbookId:t,sharedByUserId:r,sharedWithUserId:s.id,permissionLevel:a}});return n.NextResponse.json({message:"Planilha compartilhada com sucesso",share:u},{status:201})}catch(e){return p.kg.error("Erro ao compartilhar planilha:",e),n.NextResponse.json({error:"Erro ao compartilhar planilha",details:e instanceof Error?e.message:String(e)},{status:500})}}let k=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/workbooks/shared/route",pathname:"/api/workbooks/shared",filename:"route",bundlePath:"app/api/workbooks/shared/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\shared\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:b}=k,y="/api/workbooks/shared/route";function g(){return(0,i.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:x})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=a?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(o,s,n):o[s]=e[s]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},95456:(e,r,t)=>{t.d(r,{Lz:()=>a.L,rc:()=>i});var o=t(75571),a=t(81628);async function s(){return await (0,o.getServerSession)(a.L)}async function i(){let e=await s();return e?.user?.id||null}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628],()=>t(22750));module.exports=o})();