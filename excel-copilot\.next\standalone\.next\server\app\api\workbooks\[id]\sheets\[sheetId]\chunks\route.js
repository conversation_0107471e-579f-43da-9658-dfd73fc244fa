"use strict";(()=>{var e={};e.id=8134,e.ids=[8134],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},9869:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>b,requestAsyncStorage:()=>x,routeModule:()=>f,serverHooks:()=>k,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>h,dynamic:()=>p});var s=t(49303),a=t(88716),n=t(60670),i=t(87070),l=t(45609),d=t(7410),u=t(43895),c=t(63841);let p="force-dynamic",m=d.z.object({chunk:d.z.coerce.number().int().min(0),chunkSize:d.z.coerce.number().int().min(10).max(5e3).default(1e3)});async function h(e,{params:r}){try{let t;let o=await (0,l.getServerSession)();if(!o||!o.user)return i.NextResponse.json({error:"N\xe3o autorizado",details:"Autentica\xe7\xe3o necess\xe1ria"},{status:401});if(!r.id||!r.sheetId)return i.NextResponse.json({error:"Par\xe2metros inv\xe1lidos",details:"ID do workbook e da planilha s\xe3o necess\xe1rios"},{status:400});let{searchParams:s}=new URL(e.url),a=m.safeParse({chunk:s.get("chunk"),chunkSize:s.get("chunkSize")});if(!a.success)return i.NextResponse.json({error:"Par\xe2metros de query inv\xe1lidos",details:a.error.format()},{status:400});let{chunk:n,chunkSize:d}=a.data;if(!await c.prisma.workbook.findUnique({where:{id:r.id,userId:o.user.id},select:{id:!0}}))return i.NextResponse.json({error:"N\xe3o encontrado",details:"Workbook n\xe3o encontrado ou sem permiss\xe3o de acesso"},{status:404});let p=await c.prisma.sheet.findUnique({where:{id:r.sheetId,workbookId:r.id},select:{id:!0,name:!0,data:!0}});if(!p)return i.NextResponse.json({error:"N\xe3o encontrado",details:"Planilha n\xe3o encontrada"},{status:404});try{t=JSON.parse(p.data||"{}")}catch(e){return u.kg.error(`Erro ao parsear dados da planilha ${r.sheetId}:`,e),i.NextResponse.json({error:"Erro interno",details:"Os dados da planilha est\xe3o corrompidos"},{status:500})}if(!t||!Array.isArray(t.rows))return i.NextResponse.json({error:"Formato inv\xe1lido",details:"Dados da planilha em formato inv\xe1lido"},{status:500});let h=t.rows.length,f=n*d,x=Math.min(f+d,h),g=t.rows.slice(f,x);return i.NextResponse.json({name:p.name,headers:t.headers||[],rowsChunk:g,chunk:n,chunkSize:d,totalRows:h,startRow:f,endRow:x})}catch(e){return u.kg.error(`Erro ao obter chunk de planilha [${r.id}/${r.sheetId}]:`,e),i.NextResponse.json({error:"Erro interno",details:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/[id]/sheets/[sheetId]/chunks/route",pathname:"/api/workbooks/[id]/sheets/[sheetId]/chunks",filename:"route",bundlePath:"app/api/workbooks/[id]/sheets/[sheetId]/chunks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\sheets\\[sheetId]\\chunks\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:x,staticGenerationAsyncStorage:g,serverHooks:k}=f,v="/api/workbooks/[id]/sheets/[sheetId]/chunks/route";function b(){return(0,n.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:g})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>u});var s=t(99557),a=t.n(s);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let d={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=d.production;o=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);o.warn(t,e)}else o.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:s,extractedMetadata:a}=i(r),n={...t||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.error(n,e)},fatal:(e,r,t)=>{let{normalizedError:s,extractedMetadata:a}=i(r),n={...t||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.fatal(n,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=i(t);r.warn(o,e)}else r.warn(l(t)||{},e)},error:(e,t,o)=>{let{normalizedError:s,extractedMetadata:a}=i(t),n={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.error(n,e)},fatal:(e,t,o)=>{let{normalizedError:s,extractedMetadata:a}=i(t),n={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.fatal(n,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>i});var o=t(53524);let s={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},n=[],i=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function d(){try{await i.$disconnect(),s.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){s.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{a.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),a.averageQueryTime=n.reduce((e,r)=>e+r,0)/n.length),e.duration&&e.duration>500&&s.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),s.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{d()})}};var r=require("../../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,330,5609],()=>t(9869));module.exports=o})();