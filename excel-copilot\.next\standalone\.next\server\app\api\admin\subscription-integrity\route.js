"use strict";(()=>{var e={};e.id=8748,e.ids=[8748],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},27766:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>b,requestAsyncStorage:()=>v,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var n={};t.r(n),t.d(n,{GET:()=>p,POST:()=>f,dynamic:()=>l,runtime:()=>d});var o=t(49303),a=t(88716),i=t(60670),s=t(87070),u=t(75571),c=t(43895);t(46029),t(63841);let l="force-dynamic",d="nodejs";async function p(e){try{let e=await (0,u.getServerSession)();if(!e?.user)return s.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});return s.NextResponse.json({error:"Endpoint dispon\xedvel apenas em desenvolvimento."},{status:403})}catch(e){return c.kg.error("[SUBSCRIPTION_INTEGRITY_ERROR]",e),s.NextResponse.json({error:"Erro ao verificar integridade das assinaturas.",details:void 0},{status:500})}}async function f(e){try{let e=await (0,u.getServerSession)();if(!e?.user)return s.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});return s.NextResponse.json({error:"Corre\xe7\xe3o autom\xe1tica dispon\xedvel apenas em desenvolvimento."},{status:403})}catch(e){return c.kg.error("[SUBSCRIPTION_AUTO_FIX_ERROR]",e),s.NextResponse.json({error:"Erro ao corrigir problemas de integridade.",details:void 0},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/subscription-integrity/route",pathname:"/api/admin/subscription-integrity",filename:"route",bundlePath:"app/api/admin/subscription-integrity/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\subscription-integrity\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:v,staticGenerationAsyncStorage:g,serverHooks:y}=m,h="/api/admin/subscription-integrity/route";function b(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,t&&t.set(e,n),n}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},43895:(e,r,t)=>{let n;t.d(r,{kg:()=>l});var o=t(99557),a=t.n(o);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(n=>{r.includes(n)||(t[n]=e[n])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function u(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=c.production;n=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),n=a()({level:"info",formatters:{level:e=>({level:e})}})}let l={trace:(e,r)=>{n.trace(r||{},e)},debug:(e,r)=>{n.debug(r||{},e)},info:(e,r)=>{n.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);n.warn(t,e)}else n.warn(u(r)||{},e)},error:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.fatal(i,e)},createChild:e=>{let r=n.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:n}=s(t);r.warn(n,e)}else r.warn(u(t)||{},e)},error:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.error(i,e)},fatal:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},46029:(e,r,t)=>{t.d(r,{Ag:()=>u,Al:()=>c,DI:()=>l,Xf:()=>a,cb:()=>i}),t(30468);var n=t(31059);let o={PRO_MONTHLY:"price_1RWHbARrKLXtzZkME498Zuab",PRO_ANNUAL:"price_1RWHckRrKLXtzZkMLLn1vFvh"},a={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},i={[a.FREE]:50,[a.PRO_MONTHLY]:500,[a.PRO_ANNUAL]:1e3},s=process.env.STRIPE_SECRET_KEY||"",u=s?new n.Z(s,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function c(e){switch(e){case a.PRO_MONTHLY:return o.PRO_MONTHLY;case a.PRO_ANNUAL:return o.PRO_ANNUAL;default:return o.PRO_MONTHLY}}function l(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>u,prisma:()=>s});var n=t(53524);let o={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function u(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function c(){try{await s.$disconnect(),o.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){o.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{a.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),a.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&o.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),o.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})},30468:()=>{var e,r="https://js.stripe.com",t="".concat(r,"/").concat("basil","/stripe.js"),n=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,o=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,a=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var a,i=e[t];if(a=i.src,n.test(a)||o.test(a))return i}return null},i=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(t).concat(r);var o=document.head||document.body;if(!o)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return o.appendChild(n),n},s=null,u=null,c=null;Promise.resolve().then(function(){return e||(e=(null!==s?s:(s=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=a();n?n&&null!==c&&null!==u&&(n.removeEventListener("load",c),n.removeEventListener("error",u),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=i(null)):n=i(null),c=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},u=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",c),n.addEventListener("error",u)}catch(e){r(e);return}})).catch(function(e){return s=null,Promise.reject(e)})).catch(function(r){return e=null,Promise.reject(r)}))}).catch(function(e){console.warn(e)})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,5972,9557,330,5609,1059],()=>t(27766));module.exports=n})();