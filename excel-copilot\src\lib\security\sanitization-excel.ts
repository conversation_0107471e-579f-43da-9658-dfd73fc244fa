/**
 * Sanitização de dados específicos para arquivos Excel
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security implementada (18/06/2025)
 * <PERSON><PERSON><PERSON><PERSON> separado da sanitização geral para melhor organização
 */

import { logger } from '../logger';

/**
 * Função para sanitizar dados do Excel
 * @param sheetData Dados da planilha a serem sanitizados
 * @returns Objeto com dados sanitizados e relatório de segurança
 */
export function sanitizeExcelData(sheetData: unknown): {
  sanitizedData: unknown;
  securityReport: {
    hasDangerousFormulas: boolean;
    formulasRejected: number;
    details: Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>;
  };
} {
  // Cópia do objeto para não modificar o original
  const sanitizedData = JSON.parse(JSON.stringify(sheetData));

  // Inicializar relatório de segurança
  const securityReport = {
    hasDangerousFormulas: false,
    formulasRejected: 0,
    details: [] as Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>,
  };

  try {
    // Implementação real de sanitização - ÁREA 7 SEGURANÇA (18/06/2025)
    if (Array.isArray(sanitizedData)) {
      // Processar array de dados
      sanitizedData.forEach((row, rowIndex) => {
        if (typeof row === 'object' && row !== null) {
          Object.keys(row).forEach(columnName => {
            const cellValue = (row as any)[columnName];
            if (typeof cellValue === 'string') {
              const sanitizationResult = sanitizeCellValue(cellValue, rowIndex, columnName);
              (row as any)[columnName] = sanitizationResult.sanitizedValue;

              if (sanitizationResult.wasBlocked) {
                securityReport.hasDangerousFormulas = true;
                securityReport.formulasRejected++;
                securityReport.details.push({
                  rowIndex,
                  columnName,
                  reason: sanitizationResult.reason || 'Conteúdo malicioso detectado'
                });
              }
            }
          });
        }
      });
    } else if (typeof sanitizedData === 'object' && sanitizedData !== null) {
      // Processar objeto único
      Object.keys(sanitizedData).forEach(key => {
        const value = (sanitizedData as any)[key];
        if (typeof value === 'string') {
          const sanitizationResult = sanitizeCellValue(value, 0, key);
          (sanitizedData as any)[key] = sanitizationResult.sanitizedValue;

          if (sanitizationResult.wasBlocked) {
            securityReport.hasDangerousFormulas = true;
            securityReport.formulasRejected++;
            securityReport.details.push({
              rowIndex: 0,
              columnName: key,
              reason: sanitizationResult.reason || 'Conteúdo malicioso detectado'
            });
          }
        }
      });
    }

    // Log de segurança se fórmulas perigosas foram encontradas
    if (securityReport.hasDangerousFormulas) {
      logger.warn('Fórmulas maliciosas detectadas e bloqueadas no Excel', {
        formulasRejected: securityReport.formulasRejected,
        details: securityReport.details
      });
    }

  } catch (error) {
    logger.error('Erro durante sanitização de dados Excel', error);
    // Em caso de erro, retornar dados vazios por segurança
    return {
      sanitizedData: {},
      securityReport: {
        hasDangerousFormulas: true,
        formulasRejected: 1,
        details: [{
          rowIndex: -1,
          columnName: 'SYSTEM_ERROR',
          reason: 'Erro durante sanitização - dados bloqueados por segurança'
        }]
      }
    };
  }

  return {
    sanitizedData,
    securityReport,
  };
}

/**
 * Sanitiza um valor de célula individual
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security (18/06/2025)
 */
function sanitizeCellValue(value: string, rowIndex: number, columnName: string): {
  sanitizedValue: string;
  wasBlocked: boolean;
  reason?: string;
} {
  if (!value || typeof value !== 'string') {
    return { sanitizedValue: value, wasBlocked: false };
  }

  // Verificar fórmulas perigosas
  if (hasDangerousFormula(value)) {
    return {
      sanitizedValue: '[FÓRMULA BLOQUEADA POR SEGURANÇA]',
      wasBlocked: true,
      reason: 'Fórmula contém comandos potencialmente perigosos'
    };
  }

  // Verificar macros e scripts
  if (hasDangerousMacro(value)) {
    return {
      sanitizedValue: '[MACRO BLOQUEADA POR SEGURANÇA]',
      wasBlocked: true,
      reason: 'Macro ou script malicioso detectado'
    };
  }

  // Verificar URLs maliciosas
  if (hasDangerousUrl(value)) {
    return {
      sanitizedValue: '[URL BLOQUEADA POR SEGURANÇA]',
      wasBlocked: true,
      reason: 'URL potencialmente maliciosa detectada'
    };
  }

  // Sanitizar valor normalmente
  const sanitizedValue = sanitizeValue(value);
  return {
    sanitizedValue,
    wasBlocked: false
  };
}

/**
 * Verifica se uma fórmula contém comandos potencialmente perigosos
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security (18/06/2025)
 * @param formula Fórmula para verificar
 * @returns true se a fórmula for perigosa
 */
function hasDangerousFormula(formula: string): boolean {
  if (!formula) return false;

  // Lista expandida de padrões perigosos em fórmulas Excel
  const dangerousPatterns = [
    // Comandos de execução
    /=.*EXEC\(/i,
    /=.*CMD\(/i,
    /=.*SHELL\(/i,
    /=.*RUN\(/i,
    /=.*CALL\(/i,
    /=.*SYSTEM\(/i,

    // DDE e comunicação externa
    /=.*DDE\(/i,
    /=.*DDEEXEC\(/i,
    /=.*DDEAUTO\(/i,
    /=.*DDEPOKE\(/i,

    // Macros e VBA
    /=.*XLM\./i,
    /=.*MSExcel\./i,
    /=.*Application\./i,
    /=.*VBA\./i,
    /=.*MACRO\(/i,

    // URLs perigosas
    /=.*HYPERLINK\(".*javascript:/i,
    /=.*HYPERLINK\(".*data:/i,
    /=.*HYPERLINK\(".*file:/i,
    /=.*HYPERLINK\(".*ftp:/i,

    // Importação de dados externos
    /=.*IMPORTFROMWEB\(/i,
    /=.*WEBSERVICE\(/i,
    /=.*FILTERXML\(/i,

    // Funções de arquivo
    /=.*DOCUMENT\(/i,
    /=.*FILES\(/i,
    /=.*DIRECTORY\(/i,

    // PowerQuery e conexões externas
    /=.*POWERQUERY\(/i,
    /=.*ODBC\(/i,
    /=.*OLEDB\(/i,

    // Comandos do Windows
    /=.*WINEXEC\(/i,
    /=.*CREATEOBJECT\(/i,
    /=.*GETOBJECT\(/i,
  ];

  return dangerousPatterns.some(pattern => pattern.test(formula));
}

/**
 * Verifica se contém macros perigosas
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security (18/06/2025)
 */
function hasDangerousMacro(value: string): boolean {
  if (!value) return false;

  const macroPatterns = [
    // VBA e macros
    /Sub\s+\w+\(/i,
    /Function\s+\w+\(/i,
    /Private\s+Sub/i,
    /Public\s+Sub/i,
    /Auto_Open/i,
    /Auto_Close/i,
    /Workbook_Open/i,

    // Scripts maliciosos
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /activexobject/i,

    // Comandos do sistema
    /cmd\.exe/i,
    /powershell/i,
    /wscript/i,
    /cscript/i,
  ];

  return macroPatterns.some(pattern => pattern.test(value));
}

/**
 * Verifica se contém URLs perigosas
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security (18/06/2025)
 */
function hasDangerousUrl(value: string): boolean {
  if (!value) return false;

  const urlPatterns = [
    // Protocolos perigosos
    /javascript:/i,
    /data:/i,
    /file:/i,
    /ftp:/i,

    // URLs suspeitas
    /bit\.ly/i,
    /tinyurl/i,
    /t\.co/i,
    /goo\.gl/i,

    // IPs locais e privados
    /https?:\/\/127\./i,
    /https?:\/\/localhost/i,
    /https?:\/\/192\.168\./i,
    /https?:\/\/10\./i,
    /https?:\/\/172\.(1[6-9]|2[0-9]|3[01])\./i,
  ];

  return urlPatterns.some(pattern => pattern.test(value));
}

/**
 * Sanitiza valores de texto para remover conteúdo potencialmente perigoso
 * ÁREA 7 SEGURANÇA - Enhanced Excel Security (18/06/2025)
 * @param value Valor para sanitizar
 * @returns Valor sanitizado
 */
function sanitizeValue(value: string): string {
  if (!value || typeof value !== 'string') return value;

  // Remover tags HTML
  let sanitized = value.replace(/<[^>]*>/g, '');

  // Remover scripts e protocolos perigosos
  sanitized = sanitized.replace(/javascript:/gi, 'blocked:');
  sanitized = sanitized.replace(/data:/gi, 'blocked:');
  sanitized = sanitized.replace(/vbscript:/gi, 'blocked:');
  sanitized = sanitized.replace(/file:/gi, 'blocked:');

  // Remover caracteres de controle
  const controlCharsRegex = new RegExp(`[\\u0000-\\u001F\\u007F]`, 'g');
  sanitized = sanitized.replace(controlCharsRegex, '');

  // Remover sequências de escape perigosas
  sanitized = sanitized.replace(/\\x[0-9a-fA-F]{2}/g, '');
  sanitized = sanitized.replace(/\\u[0-9a-fA-F]{4}/g, '');

  // Limitar tamanho para prevenir ataques de DoS
  if (sanitized.length > 10000) {
    sanitized = sanitized.substring(0, 10000) + '[TRUNCADO]';
  }

  return sanitized;
}

// Export functions for testing and external use
export { hasDangerousFormula, hasDangerousMacro, hasDangerousUrl, sanitizeValue };

// Also export as default for flexibility
export default sanitizeExcelData;
