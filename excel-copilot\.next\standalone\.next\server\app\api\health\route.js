"use strict";(()=>{var e={};e.id=8829,e.ids=[8829],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},76197:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>P,patchFetch:()=>E,requestAsyncStorage:()=>x,routeModule:()=>v,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{DELETE:()=>m,GET:()=>p,POST:()=>d,PUT:()=>h,dynamic:()=>l});var s=r(49303),o=r(88716),n=r(60670),i=r(87070),u=r(60756);let c="phase-production-build"===process.env.NEXT_PHASE||"true"===process.env.NEXT_STATIC_EXPORT,l="force-dynamic";async function p(e){if(c)return i.NextResponse.json({status:"skipped",message:"Verifica\xe7\xe3o de sa\xfade ignorada durante build est\xe1tico",timestamp:new Date().toISOString()},{status:200});try{let t;let r=new URL(e.url),a=r.searchParams.get("type")||"all",s="false"!==r.searchParams.get("details");t="critical"===a?await (0,u.checkCriticalServices)():await (0,u.checkAllServices)();let o=(0,u.healthStatusToHttpCode)(t.overall),n=(0,u.formatHealthResponse)(t,s);return i.NextResponse.json(n,{status:o,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return i.NextResponse.json({status:"unhealthy",message:"Erro ao verificar status do sistema",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error"},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function d(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function h(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function m(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let v=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:g,serverHooks:f}=v,P="/api/health/route";function E(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,756],()=>r(76197));module.exports=a})();