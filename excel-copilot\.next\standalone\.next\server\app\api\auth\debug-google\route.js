"use strict";(()=>{var e={};e.id=8465,e.ids=[8465],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},69356:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>l,staticGenerationAsyncStorage:()=>g});var r={};o.r(r),o.d(r,{GET:()=>p,dynamic:()=>i});var n=o(49303),a=o(88716),s=o(60670),u=o(87070);let i="force-dynamic";async function p(){try{let e=process.env.AUTH_GOOGLE_CLIENT_ID,t=`${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/google`;if(!e||!t)return u.NextResponse.json({error:"Configura\xe7\xe3o incompleta",message:"Configura\xe7\xf5es OAuth ausentes"},{status:500});let o=["profile","email"].map(e=>encodeURIComponent(e)).join("%20"),r=Math.random().toString(36).substring(2,15),n=`https://accounts.google.com/o/oauth2/v2/auth?client_id=${encodeURIComponent(e)}&redirect_uri=${encodeURIComponent(t)}&response_type=code&scope=${o}&state=${r}&prompt=consent&access_type=offline`;return u.NextResponse.redirect(n)}catch{return u.NextResponse.json({error:"Erro interno",message:"Ocorreu um erro ao iniciar autentica\xe7\xe3o Google"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/debug-google/route",pathname:"/api/auth/debug-google",filename:"route",bundlePath:"app/api/auth/debug-google/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-google\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:l}=c,m="/api/auth/debug-google/route";function h(){return(0,s.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[8948,5972],()=>o(69356));module.exports=r})();