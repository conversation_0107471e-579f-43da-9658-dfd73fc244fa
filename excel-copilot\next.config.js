/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      'lh3.googleusercontent.com', // Para imagens de usuários do Google
      'github.com', // Para avatares do GitHub
      'avatars.githubusercontent.com', // Para avatares do GitHub
      'avatar.vercel.sh',
    ],
    // Permitir arquivos SVG como imagens seguras
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Configuração experimental otimizada para builds rápidos
  experimental: {
    // Configurações para acelerar builds
    scrollRestoration: true,
    forceSwcTransforms: true, // Força uso do SWC (20x mais rápido)
    webpackBuildWorker: true, // Worker threads para webpack
    optimizeCss: true, // Otimização de CSS
    serverComponentsExternalPackages: ['@google-cloud/vertexai', 'googleapis', 'exceljs'],
  },

  // Configuração condicional para builds locais
  ...(process.env.DISABLE_STATIC_GENERATION === 'true' && {
    generateStaticParams: false,
    staticPageGenerationTimeout: 0,
  }),
  // Configuração específica para o compilador
  compiler: {
    // Remover console.log em produção
    removeConsole:
      process.env.NODE_ENV === 'production'
        ? {
            exclude: ['error', 'warn'],
          }
        : false,
  },
  // Ignorar ESLint em build
  eslint: {
    // Ignorar erros de ESLint durante builds para permitir deploys mesmo com warnings
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Ignorar erros de TypeScript durante builds para permitir deploys mesmo com erros
    ignoreBuildErrors: true,
  },
  // Compressão de arquivos estáticos
  compress: true,
  // Configuração para rotas que usam headers e não podem ser estáticas
  excludeDefaultMomentLocales: true,
  // Marcar rotas específicas como dinâmicas para evitar erros de build
  serverRuntimeConfig: {
    dynamicRoutes: ['/api/api-docs', '/api/metrics', '/api/ws', '/api/socket'],
  },
  // Configurações de segurança para headers HTTP
  async headers() {
    return [
      {
        // Aplicar headers em todas as rotas
        source: '/:path*',
        headers: [
          // Headers de segurança padrão
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          // Content Security Policy rigorosa - ÁREA 7 SEGURANÇA IMPLEMENTADA (18/06/2025)
          {
            key: 'Content-Security-Policy',
            value:
              "default-src 'self'; script-src 'self' 'nonce-excel-copilot' https://cdn.jsdelivr.net https://apis.google.com https://js.stripe.com https://accounts.google.com https://github.com https://va.vercel-scripts.com; script-src-elem 'self' 'nonce-excel-copilot' https://js.stripe.com https://cdn.jsdelivr.net https://apis.google.com https://accounts.google.com https://github.com https://va.vercel-scripts.com; worker-src 'self' blob:; style-src 'self' 'nonce-excel-copilot' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com; img-src 'self' data: https: blob:; font-src 'self' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com data:; connect-src 'self' https://api.openai.com https://api.stripe.com https://*.vercel.app https://*.googleapis.com https://*.google.com https://github.com https://accounts.google.com https://va.vercel-scripts.com https://vitals.vercel-insights.com https://*.supabase.co; frame-src 'self' https://js.stripe.com https://accounts.google.com https://github.com; object-src 'none'; base-uri 'self'; form-action 'self' https://accounts.google.com https://github.com; frame-ancestors 'self'; upgrade-insecure-requests; require-trusted-types-for 'script';",
          },
          // Permissões modernas com Permissions-Policy
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
          },
        ],
      },
    ];
  },
  // Configuração de webpack OTIMIZADA para builds rápidos
  webpack: (config, { isServer, webpack }) => {
    // Cache otimizado para builds mais rápidos
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
    };

    // Configuração para servidor
    if (isServer) {
      // Externals essenciais para o servidor
      config.externals = config.externals || [];
      if (Array.isArray(config.externals)) {
        config.externals.push({
          '@google-cloud/aiplatform': 'commonjs @google-cloud/aiplatform',
          '@google-cloud/vertexai': 'commonjs @google-cloud/vertexai',
          exceljs: 'commonjs exceljs', // ExcelJS como external no servidor
        });
      }
    }

    // Configuração mínima para cliente - apenas o essencial
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        http2: false,
        // Apenas polyfills essenciais
        process: require.resolve('process/browser'),
        buffer: require.resolve('buffer'),
        // Bloquear módulos de IA no cliente
        '@google-cloud/vertexai': false,
        '@google-cloud/aiplatform': false,
        // Fix para oidc-token-hash no Edge Runtime
        'oidc-token-hash': false,
        'openid-client': false,
      };
    }

    // Configuração específica para Edge Runtime (middleware)
    config.resolve.alias = {
      ...config.resolve.alias,
      // Alias para resolver problemas do oidc-token-hash no Edge Runtime
      'oidc-token-hash/lib/shake256': false,
    };

    return config;
  },
  // Opções de análise para produção
  productionBrowserSourceMaps: false,
  poweredByHeader: false,
  // Configurações para exportação de builds
  output: 'standalone',
  // Desabilitar exportação estática completamente
  staticPageGenerationTimeout: 180,
  // Configurações para internacionalização - REMOVIDO para acelerar build
  // Transpilar apenas pacotes necessários (removidos pacotes Google para evitar problemas no cliente)
  transpilePackages: [
    // Removidos pacotes Google que devem ser server-only
  ],
  // Configuração de redirecionamentos para migração do Pages Router para App Router
  async redirects() {
    return [
      // Redirecionar APIs antigas para o manipulador de redirecionamento
      {
        source: '/api/:path*',
        has: [
          {
            type: 'header',
            key: 'x-legacy-api',
            value: '(?<legacyValue>.*)',
          },
        ],
        destination: '/api/legacy-redirect/:path*',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
