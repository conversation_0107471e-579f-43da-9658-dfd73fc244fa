"use strict";(()=>{var e={};e.id=3003,e.ids=[3003],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},55218:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>A,patchFetch:()=>_,requestAsyncStorage:()=>m,routeModule:()=>R,serverHooks:()=>I,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>p,POST:()=>E,dynamic:()=>d,runtime:()=>c});var n=t(49303),s=t(88716),a=t(60670),i=t(43895),u=t(89314),l=t(82840);let d="force-dynamic",c="nodejs";async function p(e){try{let r=process.env.MCP_VERCEL_TOKEN,t=process.env.MCP_VERCEL_PROJECT_ID,o=process.env.MCP_VERCEL_TEAM_ID;if(!r)return l.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let{searchParams:n}=new URL(e.url),s=parseInt(n.get("limit")||"20"),a=n.get("state");if(s<1||s>100)return l.R.error("Par\xe2metro limit deve estar entre 1 e 100","INVALID_PARAMETER",400);if(a&&!["BUILDING","ERROR","INITIALIZING","QUEUED","READY","CANCELED"].includes(a))return l.R.error("Par\xe2metro state inv\xe1lido","INVALID_PARAMETER",400);let d={apiToken:r};o&&(d.teamId=o),t&&(d.projectId=t);let c=new u.w(d),p=await c.getDeployments(s),E=(a?p.filter(e=>e.state===a):p).map(e=>({id:e.uid,name:e.name,url:e.url,state:e.state,target:e.target,source:e.source,created:new Date(e.created).toISOString(),buildingAt:e.buildingAt?new Date(e.buildingAt).toISOString():null,ready:e.ready?new Date(e.ready).toISOString():null,duration:e.ready&&e.buildingAt?e.ready-e.buildingAt:null,alias:e.alias,inspectorUrl:e.inspectorUrl})),R={deployments:E,total:E.length,filters:{limit:s,state:a||"all"},timestamp:new Date().toISOString()};return i.kg.info("Deployments Vercel obtidos com sucesso",{count:E.length,state:a}),l.R.success(R)}catch(e){if(i.kg.error("Erro ao obter deployments do Vercel",{error:e}),e instanceof Error)return l.R.error(`Erro ao conectar com Vercel: ${e.message}`,"VERCEL_API_ERROR",500);return l.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function E(e){try{let r=process.env.MCP_VERCEL_TOKEN,t=process.env.MCP_VERCEL_PROJECT_ID,o=process.env.MCP_VERCEL_TEAM_ID;if(!r)return l.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let{deploymentId:n,includeLogs:s=!1}=await e.json();if(!n)return l.R.error("deploymentId \xe9 obrigat\xf3rio","INVALID_PARAMETER",400);let a={apiToken:r};o&&(a.teamId=o),t&&(a.projectId=t);let d=new u.w(a),c=await d.getDeployment(n),p={id:c.uid,name:c.name,url:c.url,state:c.state,target:c.target,source:c.source,created:new Date(c.created).toISOString(),buildingAt:c.buildingAt?new Date(c.buildingAt).toISOString():null,ready:c.ready?new Date(c.ready).toISOString():null,duration:c.ready&&c.buildingAt?c.ready-c.buildingAt:null,alias:c.alias,inspectorUrl:c.inspectorUrl,meta:c.meta},E=null;if(s)try{E=(await d.getDeploymentLogs(n,{limit:100})).map(e=>({timestamp:new Date(e.timestamp).toISOString(),level:e.level,source:e.source,message:e.message,requestId:e.requestId,region:e.region}))}catch(e){i.kg.warn(`Erro ao obter logs do deployment ${n}`,e),E=[]}let R={deployment:p,logs:E,timestamp:new Date().toISOString()};return i.kg.info("Detalhes do deployment obtidos com sucesso",{deploymentId:n,state:c.state,includeLogs:s}),l.R.success(R)}catch(e){if(i.kg.error("Erro ao obter detalhes do deployment",{error:e}),e instanceof Error)return l.R.error(`Erro ao conectar com Vercel: ${e.message}`,"VERCEL_API_ERROR",500);return l.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let R=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/vercel/deployments/route",pathname:"/api/vercel/deployments",filename:"route",bundlePath:"app/api/vercel/deployments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\deployments\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:I}=R,A="/api/vercel/deployments/route";function _(){return(0,a.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:g})}},82840:(e,r,t)=>{t.d(r,{R:()=>s});var o=t(87070),n=t(43895);let s={success(e,r,t=200){let n={data:e,...r&&{meta:r}};return o.NextResponse.json(n,{status:t})},error(e,r="INTERNAL_ERROR",t=500,s){let a={code:r,message:e,timestamp:new Date().toISOString(),...void 0!==s&&{details:s}};return n.kg.error(`API Error [${r}]: ${e}`,{details:s}),o.NextResponse.json(a,{status:t})},unauthorized(e="N\xe3o autorizado",r){return this.error(e,"UNAUTHORIZED",401,r)},badRequest(e,r){return this.error(e,"BAD_REQUEST",400,r)},notFound(e="Recurso n\xe3o encontrado",r){return this.error(e,"NOT_FOUND",404,r)},forbidden(e="Acesso negado",r){return this.error(e,"FORBIDDEN",403,r)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r){let t={};return r&&(t["Retry-After"]=r.toString()),o.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:t})}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,9314],()=>t(55218));module.exports=o})();