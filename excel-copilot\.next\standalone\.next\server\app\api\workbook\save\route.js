"use strict";(()=>{var e={};e.id=6240,e.ids=[6240],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24221:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>k,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w});var t={};o.r(t),o.d(t,{POST:()=>x,dynamic:()=>l,runtime:()=>p});var a=o(49303),s=o(88716),n=o(60670),i=o(87070),d=o(45609),u=o(7410),c=o(63841);let l="force-dynamic",p="nodejs",m=u.z.object({workbookId:u.z.string().optional(),name:u.z.string().min(1,"Nome \xe9 obrigat\xf3rio"),description:u.z.string().optional(),sheets:u.z.array(u.z.object({id:u.z.string().optional(),name:u.z.string().min(1,"Nome da planilha \xe9 obrigat\xf3rio"),data:u.z.any()})).min(1,"Pelo menos uma planilha \xe9 necess\xe1ria")});async function x(e){try{let r=await (0,d.getServerSession)(),o=await e.json(),t=m.safeParse(o);if(!t.success)return i.NextResponse.json({error:"Dados inv\xe1lidos",details:t.error.format()},{status:400});let{workbookId:a,name:s,description:n,sheets:u}=t.data,l=r?.user?r.user.id:"guest";if(a){if(!await c.prisma.workbook.findUnique({where:{id:a,userId:l},include:{sheets:!0}}))return i.NextResponse.json({error:"Planilha n\xe3o encontrada ou sem permiss\xe3o"},{status:404});let e=await c.prisma.workbook.update({where:{id:a},data:{name:s,description:n||null,updatedAt:new Date}});for(let r of u){let o=JSON.stringify(r.data);r.id?await c.prisma.sheet.update({where:{id:r.id},data:{name:r.name,data:o,updatedAt:new Date}}):await c.prisma.sheet.create({data:{name:r.name,data:o,workbookId:e.id}})}return i.NextResponse.json({success:!0,workbookId:e.id,message:"Planilha atualizada com sucesso"})}{let e=await c.prisma.workbook.create({data:{name:s,description:n||null,userId:l,isPublic:!1,sheets:{create:u.map(e=>({name:e.name,data:JSON.stringify(e.data)}))}}});return i.NextResponse.json({success:!0,workbookId:e.id,message:"Planilha criada com sucesso"})}}catch(r){console.error("Erro ao salvar planilha:",r);let e=r instanceof Error?r.message:"Erro desconhecido";return i.NextResponse.json({error:e},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/workbook/save/route",pathname:"/api/workbook/save",filename:"route",bundlePath:"app/api/workbook/save/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbook\\save\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:f}=h,k="/api/workbook/save/route";function b(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},63841:(e,r,o)=>{o.d(r,{P:()=>d,prisma:()=>i});var t=o(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},n=[],i=global.prisma||new t.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function d(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function u(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{s.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),s.averageQueryTime=n.reduce((e,r)=>e+r,0)/n.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8948,5972,7410,330,5609],()=>o(24221));module.exports=t})();