"use strict";(()=>{var e={};e.id=2145,e.ids=[2145],e.modules={53524:e=>{e.exports=require("@prisma/client")},57641:e=>{e.exports=require("exceljs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},31412:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>w,patchFetch:()=>b,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>C,staticGenerationAsyncStorage:()=>y});var o={};t.r(o),t.d(o,{GET:()=>x,dynamic:()=>m,runtime:()=>f});var a=t(49303),n=t(88716),s=t(60670),i=t(57641),l=t.n(i),c=t(87070),d=t(45609),u=t(43895),p=t(63841);let m="force-dynamic",f="nodejs";async function x(e,{params:r}){try{let e=await (0,d.getServerSession)();if(!e?.user)return c.NextResponse.json({error:"N\xe3o autorizado"},{status:401});let t=r.id,o=await p.prisma.workbook.findUnique({where:{id:t},include:{sheets:!0}});if(!o)return c.NextResponse.json({error:"Planilha n\xe3o encontrada"},{status:404});if(o.userId!==e.user?.id&&!o.isPublic)return c.NextResponse.json({error:"Acesso negado a esta planilha"},{status:403});let a=new(l()).Workbook;for(let r of(a.creator="Excel Copilot",a.lastModifiedBy=e.user.name||"Usu\xe1rio",a.created=new Date(o.createdAt),a.modified=new Date(o.updatedAt),o.name,o.description,o.sheets)){let e=a.addWorksheet(r.name);if(r.data)try{let t="string"==typeof r.data?JSON.parse(r.data):r.data;t&&t.headers&&Array.isArray(t.headers)&&(e.columns=t.headers.map(e=>({header:e,key:e,width:Math.max(1.2*e.length,10)})),t.rows&&Array.isArray(t.rows)&&e.addRows(t.rows),t.headers.length>0&&(e.getRow(1).font={bold:!0},e.getRow(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFE6F0FF"}},e.eachRow({includeEmpty:!1},(e,r)=>{e.eachCell({includeEmpty:!1},e=>{e.border={top:{style:"thin",color:{argb:"FFCCCCCC"}},left:{style:"thin",color:{argb:"FFCCCCCC"}},bottom:{style:"thin",color:{argb:"FFCCCCCC"}},right:{style:"thin",color:{argb:"FFCCCCCC"}}}})})))}catch(e){u.kg.error(`Erro ao processar dados da planilha ${r.id}:`,e)}}let n=await a.xlsx.writeBuffer(),s=function(e){let r=e&&e.replace(/[\\/:*?"<>|]/g,"").replace(/\s+/g,"_").substring(0,255)||"planilha";if(/^[!-~]*$/.test(r))return`attachment; filename="${r}.xlsx"`;{let e=encodeURIComponent(r).replace(/%20/g,"_");return`attachment; filename="${r}.xlsx"; filename*=UTF-8''${e}.xlsx`}}(o.name);return new c.NextResponse(n,{headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","Content-Disposition":s,"Cache-Control":"no-store, max-age=0"}})}catch(e){return u.kg.error("[EXCEL_EXPORT_ERROR]",e),c.NextResponse.json({error:"Erro ao gerar o arquivo Excel"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/workbooks/[id]/export/route",pathname:"/api/workbooks/[id]/export",filename:"route",bundlePath:"app/api/workbooks/[id]/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\export\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:C}=g,w="/api/workbooks/[id]/export/route";function b(){return(0,s.patchFetch)({serverHooks:C,staticGenerationAsyncStorage:y})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>d});var a=t(99557),n=t.n(a);function s(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:s(e),extractedMetadata:e}:{normalizedError:s(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:n().stdSerializers.err,error:n().stdSerializers.err}}};try{let e=c.production;o=n()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=n()({level:"info",formatters:{level:e=>({level:e})}})}let d={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);o.warn(t,e)}else o.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:a,extractedMetadata:n}=i(r),s={...t||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.error(s,e)},fatal:(e,r,t)=>{let{normalizedError:a,extractedMetadata:n}=i(r),s={...t||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.fatal(s,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=i(t);r.warn(o,e)}else r.warn(l(t)||{},e)},error:(e,t,o)=>{let{normalizedError:a,extractedMetadata:n}=i(t),s={...o||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.error(s,e)},fatal:(e,t,o)=>{let{normalizedError:a,extractedMetadata:n}=i(t),s={...o||{},...n,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.fatal(s,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>i});var o=t(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},n={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},s=[],i=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...n,activeConnections:Math.min(Math.floor(5*Math.random())+1,n.maxPoolSize),poolSize:n.poolSize}}async function c(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{n.totalQueries++,e.duration&&(s.push(e.duration),s.length>100&&s.shift(),n.averageQueryTime=s.reduce((e,r)=>e+r,0)/s.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{n.failedQueries++,n.connectionFailures++,n.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,330,5609],()=>t(31412));module.exports=o})();