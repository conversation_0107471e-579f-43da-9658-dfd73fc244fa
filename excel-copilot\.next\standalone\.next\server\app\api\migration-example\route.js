"use strict";(()=>{var e={};e.id=217,e.ids=[217],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39778:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>l,patchFetch:()=>x,requestAsyncStorage:()=>m,routeModule:()=>u,serverHooks:()=>c,staticGenerationAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>s});var o=r(49303),i=r(88716),n=r(60670),p=r(87070);async function s(e){return p.NextResponse.json({message:"Dados obtidos com sucesso",approach:"manual app router",timestamp:new Date().toISOString()})}let u=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/migration-example/route",pathname:"/api/migration-example",filename:"route",bundlePath:"app/api/migration-example/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\migration-example\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:d,serverHooks:c}=u,l="/api/migration-example/route";function x(){return(0,n.patchFetch)({serverHooks:c,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972],()=>r(39778));module.exports=a})();