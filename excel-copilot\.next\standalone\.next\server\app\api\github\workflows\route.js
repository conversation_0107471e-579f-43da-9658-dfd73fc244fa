"use strict";(()=>{var e={};e.id=8535,e.ids=[8535],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},83128:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>f,requestAsyncStorage:()=>R,routeModule:()=>h,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>p,dynamic:()=>c});var s=t(49303),o=t(88716),u=t(60670),n=t(51557),i=t(43895),l=t(82840);let c="force-dynamic";async function d(e){try{let r=process.env.MCP_GITHUB_TOKEN,t=process.env.MCP_GITHUB_OWNER,a=process.env.MCP_GITHUB_REPO;if(!r)return l.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let{searchParams:s}=new URL(e.url),o=s.get("owner")||t,u=s.get("repo")||a,c=s.get("workflow_id"),d=s.get("actor"),p=s.get("branch"),h=s.get("event"),R=s.get("status"),g=parseInt(s.get("per_page")||"30"),w=parseInt(s.get("page")||"1"),_="true"===s.get("include_metrics");if(!o||!u)return l.R.badRequest("owner e repo s\xe3o obrigat\xf3rios");if(g<1||g>100)return l.R.badRequest("Par\xe2metro per_page deve estar entre 1 e 100");if(w<1)return l.R.badRequest("Par\xe2metro page deve ser maior que 0");let f=new n.e({token:r}),m=await f.getWorkflowRuns({owner:o,repo:u,...c&&{workflow_id:c},...d&&{actor:d},...p&&{branch:p},...h&&{event:h},...R&&{status:R},per_page:g,page:w}),b=m.workflowRuns.map(e=>({id:e.id,name:e.name,headBranch:e.head_branch,headSha:e.head_sha,status:e.status,conclusion:e.conclusion,workflowId:e.workflow_id,htmlUrl:e.html_url,createdAt:e.created_at,updatedAt:e.updated_at,runStartedAt:e.run_started_at,jobsUrl:e.jobs_url,logsUrl:e.logs_url,checkSuiteUrl:e.check_suite_url,artifactsUrl:e.artifacts_url,cancelUrl:e.cancel_url,rerunUrl:e.rerun_url,workflowUrl:e.workflow_url,pullRequests:e.pull_requests.map(e=>({id:e.id,number:e.number,url:e.url,head:{ref:e.head.ref,sha:e.head.sha},base:{ref:e.base.ref,sha:e.base.sha}}))})),I=null;if(_)try{let e=new n.w({token:r});I=await e.getCICDMetrics(o,u)}catch(e){i.kg.warn("Erro ao obter m\xe9tricas de CI/CD:",e)}let k={repository:`${o}/${u}`,workflowRuns:b,pagination:{page:w,perPage:g,total:m.total,hasNext:b.length===g},filters:{workflowId:c,actor:d,branch:p,event:h,status:R},metrics:I?{totalRuns:I.totalRuns,successRate:I.successRate,averageDuration:I.averageDuration,recentFailures:I.recentFailures.length,trends:I.trendsLast30Days}:null,timestamp:new Date().toISOString()};return i.kg.info("Workflow runs GitHub obtidos com sucesso",{repository:`${o}/${u}`,count:b.length,includeMetrics:_}),l.R.success(k)}catch(e){if(i.kg.error("Erro ao obter workflow runs do GitHub",{error:e}),e instanceof Error)return l.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return l.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function p(e){try{let r=process.env.MCP_GITHUB_TOKEN;if(!r)return l.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let{owner:t,repo:a,days:s=30}=await e.json();if(!t||!a)return l.R.badRequest("owner e repo s\xe3o obrigat\xf3rios");if(s<1||s>90)return l.R.badRequest("Par\xe2metro days deve estar entre 1 e 90");let o=new n.w({token:r}),u=await o.getCICDMetrics(t,a),c=await o.getRepositoryDashboard(t,a),d={repository:`${t}/${a}`,period:`${s} days`,metrics:{totalRuns:u.totalRuns,successRate:u.successRate,averageDuration:u.averageDuration,recentFailures:u.recentFailures.map(e=>({id:e.id,name:e.name,headBranch:e.head_branch,headSha:e.head_sha,conclusion:e.conclusion,htmlUrl:e.html_url,createdAt:e.created_at,updatedAt:e.updated_at})),trends:u.trendsLast30Days},repository_health:c.healthStatus,summary:{status:c.healthStatus,openIssues:c.openIssues,openPullRequests:c.openPullRequests,lastUpdate:c.repository.updated_at},timestamp:new Date().toISOString()};return i.kg.info("M\xe9tricas detalhadas de CI/CD obtidas com sucesso",{repository:`${t}/${a}`,totalRuns:u.totalRuns,successRate:u.successRate}),l.R.success(d)}catch(e){if(i.kg.error("Erro ao obter m\xe9tricas de CI/CD do GitHub",{error:e}),e instanceof Error)return l.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return l.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let h=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/github/workflows/route",pathname:"/api/github/workflows",filename:"route",bundlePath:"app/api/github/workflows/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\workflows\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:R,staticGenerationAsyncStorage:g,serverHooks:w}=h,_="/api/github/workflows/route";function f(){return(0,u.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557,7410,2972,5072],()=>t(83128));module.exports=a})();