"use strict";(()=>{var e={};e.id=8251,e.ids=[8251],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},77120:(e,s,r)=>{r.r(s),r.d(s,{originalPathname:()=>h,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>v,staticGenerationAsyncStorage:()=>E});var t={};r.r(t),r.d(t,{GET:()=>p,dynamic:()=>c});var n=r(49303),o=r(88716),a=r(60670),i=r(87070);let c="force-dynamic";async function p(e){try{let e={GOOGLE_CLIENT_ID:process.env.AUTH_GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET:process.env.AUTH_GOOGLE_CLIENT_SECRET,GITHUB_CLIENT_ID:process.env.AUTH_GITHUB_CLIENT_ID,GITHUB_CLIENT_SECRET:process.env.AUTH_GITHUB_CLIENT_SECRET,NEXTAUTH_SECRET:process.env.AUTH_NEXTAUTH_SECRET,NEXTAUTH_URL:process.env.AUTH_NEXTAUTH_URL,NODE_ENV:"production",VERCEL:process.env.VERCEL,VERCEL_ENV:process.env.VERCEL_ENV},s={timestamp:new Date().toISOString(),environment:"production",isVercel:!!process.env.VERCEL,vercelEnv:process.env.VERCEL_ENV,variables:Object.entries(e).map(([e,s])=>({name:e,exists:!!s,length:s?.length||0,preview:s?`${s.substring(0,10)}...`:"undefined",isEmpty:""===s,isUndefined:void 0===s})),issues:[],recommendations:[]};return Object.entries(e).forEach(([e,r])=>{r?""===r?s.issues.push(`${e} est\xe1 vazio`):r.length<10&&s.issues.push(`${e} muito curto (${r.length} caracteres)`):s.issues.push(`${e} n\xe3o est\xe1 definido`)}),process.env.VERCEL||s.issues.push("N\xe3o est\xe1 rodando no Vercel em produ\xe7\xe3o"),s.issues.length>0&&(s.recommendations.push("Verificar configura\xe7\xe3o das vari\xe1veis de ambiente no Vercel Dashboard"),s.recommendations.push("Confirmar que as vari\xe1veis est\xe3o definidas para o ambiente de produ\xe7\xe3o"),s.recommendations.push("Fazer redeploy ap\xf3s configurar as vari\xe1veis")),i.NextResponse.json(s,{status:200,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return i.NextResponse.json({error:"Erro interno durante verifica\xe7\xe3o de ambiente",message:e instanceof Error?e.message:"Erro desconhecido",timestamp:new Date().toISOString()},{status:500,headers:{"Content-Type":"application/json"}})}}let u=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/check-env/route",pathname:"/api/auth/check-env",filename:"route",bundlePath:"app/api/auth/check-env/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\check-env\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:d,staticGenerationAsyncStorage:E,serverHooks:v}=u,h="/api/auth/check-env/route";function m(){return(0,a.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:E})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,5972],()=>r(77120));module.exports=t})();