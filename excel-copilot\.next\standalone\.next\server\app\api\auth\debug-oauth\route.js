"use strict";(()=>{var e={};e.id=550,e.ids=[550],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14155:(e,o,s)=>{s.r(o),s.d(o,{originalPathname:()=>l,patchFetch:()=>U,requestAsyncStorage:()=>_,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p});var r={};s.r(r),s.d(r,{GET:()=>T,dynamic:()=>E});var t=s(49303),a=s(88716),n=s(60670),u=s(87070),i=s(52972);let E="force-dynamic";async function T(){try{let e=process.env.AUTH_GOOGLE_CLIENT_ID,o=process.env.AUTH_GOOGLE_CLIENT_SECRET,s=process.env.AUTH_GITHUB_CLIENT_ID,r=process.env.AUTH_GITHUB_CLIENT_SECRET,t=process.env.AUTH_NEXTAUTH_URL,a=process.env.AUTH_NEXTAUTH_SECRET,n=process.env.AUTH_SKIP_PROVIDERS,E=`${t}/api/auth/callback/google`,T=`${t}/api/auth/callback/github`,c={IS_PRODUCTION:i.Vi.IS_PRODUCTION,IS_DEVELOPMENT:i.Vi.IS_DEVELOPMENT,SKIP_AUTH_PROVIDERS:i.Vi.FEATURES.SKIP_AUTH_PROVIDERS,NODE_ENV:i.Vi.NODE_ENV},_=[];e||_.push("AUTH_GOOGLE_CLIENT_ID"),o||_.push("AUTH_GOOGLE_CLIENT_SECRET"),s||_.push("AUTH_GITHUB_CLIENT_ID"),r||_.push("AUTH_GITHUB_CLIENT_SECRET"),t||_.push("AUTH_NEXTAUTH_URL"),a||_.push("AUTH_NEXTAUTH_SECRET");let p=0===_.length;return u.NextResponse.json({status:p?"success":"error",message:p?"Configura\xe7\xe3o OAuth completa":"Configura\xe7\xe3o OAuth incompleta",environment:c,variables:{GOOGLE_CLIENT_ID:e?"✅ Configurado":"❌ Ausente",GOOGLE_CLIENT_SECRET:o?"✅ Configurado":"❌ Ausente",GITHUB_CLIENT_ID:s?"✅ Configurado":"❌ Ausente",GITHUB_CLIENT_SECRET:r?"✅ Configurado":"❌ Ausente",NEXTAUTH_URL:t?"✅ Configurado":"❌ Ausente",NEXTAUTH_SECRET:a?"✅ Configurado":"❌ Ausente",NODE_ENV:"production",SKIP_AUTH_PROVIDERS:n||"undefined"},missingVariables:_,callbackUrls:{google:E,github:T},recommendations:_.length>0?["Verifique se todas as vari\xe1veis de ambiente est\xe3o configuradas na Vercel","Confirme se os valores n\xe3o cont\xeam espa\xe7os ou caracteres especiais","Verifique se o NEXTAUTH_URL corresponde ao dom\xednio de produ\xe7\xe3o","Confirme se as URLs de callback est\xe3o configuradas corretamente no Google/GitHub"]:["Configura\xe7\xe3o OAuth est\xe1 completa","Verifique se as URLs de callback est\xe3o corretas nos provedores OAuth"]})}catch(e){return u.NextResponse.json({status:"error",message:"Erro ao verificar configura\xe7\xe3o OAuth",error:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let c=new t.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/debug-oauth/route",pathname:"/api/auth/debug-oauth",filename:"route",bundlePath:"app/api/auth/debug-oauth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-oauth\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:_,staticGenerationAsyncStorage:p,serverHooks:d}=c,l="/api/auth/debug-oauth/route";function U(){return(0,n.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:p})}}};var o=require("../../../../webpack-runtime.js");o.C(e);var s=e=>o(o.s=e),r=o.X(0,[8948,5972,7410,2972],()=>s(14155));module.exports=r})();