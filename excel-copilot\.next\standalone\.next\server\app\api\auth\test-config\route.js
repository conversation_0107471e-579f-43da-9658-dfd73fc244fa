"use strict";(()=>{var e={};e.id=5952,e.ids=[5952],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},17747:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>k,routeModule:()=>v,serverHooks:()=>T,staticGenerationAsyncStorage:()=>A});var r={};s.r(r),s.d(r,{GET:()=>g,POST:()=>p,dynamic:()=>l,runtime:()=>d});var a=s(49303),o=s(88716),n=s(60670),c=s(87070),i=s(52972),u=s(52202),h=s(43895);let l="force-dynamic",d="nodejs";async function g(e){try{h.kg.info("\uD83E\uDDEA Teste de configura\xe7\xe3o OAuth solicitado",{ip:e.ip,userAgent:e.headers.get("user-agent"),timestamp:new Date().toISOString()});let t={timestamp:new Date().toISOString(),environment:i.Vi.NODE_ENV,tests:{google:await _(),github:await f(),nextauth:m(),database:await E()}},s=Object.values(t.tests).every(e=>"pass"===e.status),r=s?200:503;return h.kg.info(s?"✅ Teste de configura\xe7\xe3o: APROVADO":"⚠️ Teste de configura\xe7\xe3o: PROBLEMAS",{statusCode:r,googleStatus:t.tests.google.status,githubStatus:t.tests.github.status,nextauthStatus:t.tests.nextauth.status,databaseStatus:t.tests.database.status}),c.NextResponse.json({status:s?"healthy":"unhealthy",...t},{status:r})}catch(e){return h.kg.error("❌ Erro no teste de configura\xe7\xe3o OAuth",{error:e instanceof Error?e.message:"Erro desconhecido",stack:e instanceof Error?e.stack:void 0}),c.NextResponse.json({status:"error",message:"Erro interno no teste de configura\xe7\xe3o",timestamp:new Date().toISOString()},{status:500})}}async function p(e){try{let t;let{provider:s,detailed:r=!1}=await e.json().catch(()=>({}));if(h.kg.info("\uD83D\uDD2C Teste detalhado de configura\xe7\xe3o OAuth",{provider:s,detailed:r,ip:e.ip,timestamp:new Date().toISOString()}),s)switch(s.toLowerCase()){case"google":t={google:await _(r)};break;case"github":t={github:await f(r)};break;default:return c.NextResponse.json({error:"Provider n\xe3o suportado. Use: google, github"},{status:400})}else t={google:await _(r),github:await f(r),nextauth:m(),database:await E()};let a=Object.values(t).every(e=>"pass"===e.status);return c.NextResponse.json({status:a?"healthy":"unhealthy",timestamp:new Date().toISOString(),environment:i.Vi.NODE_ENV,tests:t},{status:a?200:503})}catch(e){return h.kg.error("❌ Erro no teste detalhado de configura\xe7\xe3o",{error:e instanceof Error?e.message:"Erro desconhecido"}),c.NextResponse.json({status:"error",message:"Erro interno no teste detalhado",timestamp:new Date().toISOString()},{status:500})}}async function _(e=!1){let t={provider:"google",status:"fail",checks:{},message:""};try{let s=process.env.AUTH_GOOGLE_CLIENT_ID,r=process.env.AUTH_GOOGLE_CLIENT_SECRET;if(t.checks.credentials_present=!!(s&&r),t.checks.client_id_format=s?.includes(".apps.googleusercontent.com")||!1,t.checks.client_secret_length=(r?.length||0)>=20,t.checks.validation_passed=(0,u.OF)(),e&&s&&r)try{let e=await fetch("https://accounts.google.com/.well-known/openid_configuration",{method:"GET",headers:{"User-Agent":"Excel-Copilot-Auth-Test/1.0"}});if(t.checks.google_service_reachable=e.ok,e.ok){let s=await e.json();t.checks.authorization_endpoint=!!s.authorization_endpoint,t.checks.token_endpoint=!!s.token_endpoint}}catch(e){t.checks.google_service_reachable=!1,t.checks.service_error=e instanceof Error?e.message:"Erro desconhecido"}let a=["credentials_present","client_id_format","validation_passed"].every(e=>t.checks[e]);t.status=a?"pass":"fail",t.message=a?"Configura\xe7\xe3o Google OAuth v\xe1lida":"Problemas na configura\xe7\xe3o Google OAuth"}catch(e){t.status="fail",t.message=`Erro ao testar Google OAuth: ${e instanceof Error?e.message:"Erro desconhecido"}`}return t}async function f(e=!1){let t={provider:"github",status:"fail",checks:{},message:""};try{let s=process.env.AUTH_GITHUB_CLIENT_ID,r=process.env.AUTH_GITHUB_CLIENT_SECRET;if(t.checks.credentials_present=!!(s&&r),t.checks.client_id_length=(s?.length||0)>=16,t.checks.client_secret_length=(r?.length||0)>=30,t.checks.validation_passed=(0,u.yI)(),e&&s)try{let e=await fetch("https://api.github.com/meta",{method:"GET",headers:{"User-Agent":"Excel-Copilot-Auth-Test/1.0"}});t.checks.github_service_reachable=e.ok}catch(e){t.checks.github_service_reachable=!1,t.checks.service_error=e instanceof Error?e.message:"Erro desconhecido"}let a=["credentials_present","client_id_length","validation_passed"].every(e=>t.checks[e]);t.status=a?"pass":"fail",t.message=a?"Configura\xe7\xe3o GitHub OAuth v\xe1lida":"Problemas na configura\xe7\xe3o GitHub OAuth"}catch(e){t.status="fail",t.message=`Erro ao testar GitHub OAuth: ${e instanceof Error?e.message:"Erro desconhecido"}`}return t}function m(){let e={provider:"nextauth",status:"fail",checks:{},message:""};try{if(e.checks.secret_present=!!process.env.AUTH_NEXTAUTH_SECRET,e.checks.secret_length=(process.env.AUTH_NEXTAUTH_SECRET?.length||0)>=32,e.checks.url_present=!!process.env.AUTH_NEXTAUTH_URL,process.env.AUTH_NEXTAUTH_URL)try{new URL(process.env.AUTH_NEXTAUTH_URL),e.checks.url_valid=!0,e.checks.url_https=process.env.AUTH_NEXTAUTH_URL.startsWith("https://")||i.Vi.IS_DEVELOPMENT}catch{e.checks.url_valid=!1,e.checks.url_https=!1}let t=["secret_present","secret_length","url_present","url_valid"].every(t=>e.checks[t]);e.status=t?"pass":"fail",e.message=t?"Configura\xe7\xe3o NextAuth v\xe1lida":"Problemas na configura\xe7\xe3o NextAuth"}catch(t){e.status="fail",e.message=`Erro ao testar NextAuth: ${t instanceof Error?t.message:"Erro desconhecido"}`}return e}async function E(){let e={provider:"database",status:"fail",checks:{},message:""};try{if(e.checks.url_present=!!process.env.DB_DATABASE_URL,process.env.DB_DATABASE_URL){let{PrismaClient:t}=await Promise.resolve().then(s.t.bind(s,53524,23)),r=new t;try{await r.$connect(),e.checks.connection_successful=!0,await r.user.count(),e.checks.query_successful=!0,await r.$disconnect()}catch(t){e.checks.connection_successful=!1,e.checks.connection_error=t instanceof Error?t.message:"Erro desconhecido"}}let t=["url_present","connection_successful"].every(t=>e.checks[t]);e.status=t?"pass":"fail",e.message=t?"Conex\xe3o com banco de dados v\xe1lida":"Problemas na conex\xe3o com banco de dados"}catch(t){e.status="fail",e.message=`Erro ao testar banco de dados: ${t instanceof Error?t.message:"Erro desconhecido"}`}return e}let v=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/test-config/route",pathname:"/api/auth/test-config",filename:"route",bundlePath:"app/api/auth/test-config/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-config\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:k,staticGenerationAsyncStorage:A,serverHooks:T}=v,b="/api/auth/test-config/route";function x(){return(0,n.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:A})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,5972,9557,7410,2972,2202],()=>s(17747));module.exports=r})();