"use strict";(()=>{var e={};e.id=313,e.ids=[313],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},52572:(e,s,t)=>{t.r(s),t.d(s,{originalPathname:()=>f,patchFetch:()=>S,requestAsyncStorage:()=>g,routeModule:()=>d,serverHooks:()=>w,staticGenerationAsyncStorage:()=>x});var r={};t.r(r),t.d(r,{GET:()=>c,PATCH:()=>m,POST:()=>l});var i=t(49303),a=t(88716),o=t(60670),n=t(87070),u=t(87502),p=t(43895);async function c(e){try{let{searchParams:s}=new URL(e.url),t=s.get("teamId"),r=s.get("assigneeId"),i=s.get("state"),a=parseInt(s.get("limit")||"50"),o="true"===s.get("includeArchived"),p="true"===s.get("excelCopilotOnly"),c=new u.y;if(p){let e=new u.n,s=await e.getExcelCopilotIssues();return n.NextResponse.json({status:"success",issues:s.issues,summary:s.summary,filters:{excelCopilotOnly:!0,total:s.issues.length},timestamp:new Date().toISOString()})}let l=await c.getIssues({...t&&{teamId:t},...r&&{assigneeId:r},...i&&{state:i},limit:a,includeArchived:o}),m={total:l.issues.length,byState:l.issues.reduce((e,s)=>{let t=s.state.name;return e[t]=(e[t]||0)+1,e},{}),byTeam:l.issues.reduce((e,s)=>{let t=s.team.name;return e[t]=(e[t]||0)+1,e},{})};return n.NextResponse.json({status:"success",issues:l.issues,summary:m,filters:{teamId:t,assigneeId:r,state:i,limit:a,includeArchived:o},timestamp:new Date().toISOString()})}catch(e){return p.kg.error("Erro ao listar issues Linear:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}async function l(e){try{let{teamId:s,title:t,description:r,assigneeId:i,stateId:a,priority:o,labelIds:p,type:c}=await e.json();if(!s||!t)return n.NextResponse.json({error:"teamId e title s\xe3o obrigat\xf3rios"},{status:400});let l=new u.y;if(c&&["bug","feature","improvement"].includes(c)){let e=new u.n,s=await e.createExcelCopilotIssue({type:c,title:t,description:r||"",priority:o,assigneeId:i});return n.NextResponse.json({status:"success",action:"excel_copilot_issue_created",issue:s.issue,timestamp:new Date().toISOString()})}let m=await l.createIssue({teamId:s,title:t,description:r,assigneeId:i,stateId:a,priority:o,labelIds:p});return n.NextResponse.json({status:"success",action:"issue_created",issue:m.issue,timestamp:new Date().toISOString()})}catch(e){return p.kg.error("Erro ao criar issue Linear:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}async function m(e){try{let{issueId:s,...t}=await e.json();if(!s)return n.NextResponse.json({error:"issueId \xe9 obrigat\xf3rio"},{status:400});let r=new u.y,i=await r.updateIssue(s,t);return n.NextResponse.json({status:"success",action:"issue_updated",issue:i.issue,timestamp:new Date().toISOString()})}catch(e){return p.kg.error("Erro ao atualizar issue Linear:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/linear/issues/route",pathname:"/api/linear/issues",filename:"route",bundlePath:"app/api/linear/issues/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\issues\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:w}=d,f="/api/linear/issues/route";function S(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:x})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,5972,9557,7410,2972,7502],()=>t(52572));module.exports=r})();