"use strict";(()=>{var e={};e.id=7490,e.ids=[7490],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},400:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>d,requestAsyncStorage:()=>m,routeModule:()=>v,serverHooks:()=>x,staticGenerationAsyncStorage:()=>u});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>_,POST:()=>h,PUT:()=>n});var a=r(49303),i=r(88716),o=r(60670),l=r(87070);let c={getMetrics:()=>({uptime:Date.now(),requests:0,errors:0}),getServiceMetrics:()=>({status:"healthy",responseTime:0})};async function _(e){try{let t;let r=new URL(e.url),s=r.searchParams.get("service"),a=parseInt(r.searchParams.get("period")||"1"),i=r.searchParams.get("format")||"json";if(a<1||a>168)return l.NextResponse.json({error:"Period must be between 1 and 168 hours"},{status:400});if(s){let e=c.getServiceMetrics(s,a);t={type:"service_metrics",service:s,period:`${a}h`,data:e,timestamp:new Date().toISOString()}}else{let e=c.getSystemMetrics(a);t={type:"system_metrics",period:`${a}h`,data:e,timestamp:new Date().toISOString()}}if(t.collector_stats=c.getStats(),"prometheus"===i){let e=function(e){let t="";if(e.overall){let r=e.overall;t+=`# HELP excel_copilot_health_availability System health availability percentage
# TYPE excel_copilot_health_availability gauge
excel_copilot_health_availability ${r.availability}

# HELP excel_copilot_health_response_time_ms Average health check response time in milliseconds
# TYPE excel_copilot_health_response_time_ms gauge
excel_copilot_health_response_time_ms ${r.avgResponseTime}

# HELP excel_copilot_health_error_rate Health check error rate percentage
# TYPE excel_copilot_health_error_rate gauge
excel_copilot_health_error_rate ${r.errorRate}

# HELP excel_copilot_health_services_total Total number of monitored services
# TYPE excel_copilot_health_services_total gauge
excel_copilot_health_services_total ${r.totalServices}

# HELP excel_copilot_health_services_healthy Number of healthy services
# TYPE excel_copilot_health_services_healthy gauge
excel_copilot_health_services_healthy ${r.healthyServices}

`,e.services&&e.services.forEach(e=>{let r=e.service;t+=`# HELP excel_copilot_health_service_availability Service health availability percentage
# TYPE excel_copilot_health_service_availability gauge
`;let s=e.metrics;t+=`excel_copilot_health_service_availability{service="${r}"} ${s.availability}

# HELP excel_copilot_health_service_response_time_ms Service health check response time in milliseconds
# TYPE excel_copilot_health_service_response_time_ms gauge
excel_copilot_health_service_response_time_ms{service="${r}"} ${s.avgResponseTime}

# HELP excel_copilot_health_service_error_rate Service health check error rate percentage
# TYPE excel_copilot_health_service_error_rate gauge
excel_copilot_health_service_error_rate{service="${r}"} ${s.errorRate}

`})}else if(e.service){let r=e.service,s=e.metrics;t+=`# HELP excel_copilot_health_service_availability Service health availability percentage
# TYPE excel_copilot_health_service_availability gauge
excel_copilot_health_service_availability{service="${r}"} ${s.availability}

# HELP excel_copilot_health_service_response_time_ms Service health check response time metrics
# TYPE excel_copilot_health_service_response_time_ms gauge
excel_copilot_health_service_response_time_ms{service="${r}",quantile="avg"} ${s.avgResponseTime}
excel_copilot_health_service_response_time_ms{service="${r}",quantile="min"} ${s.minResponseTime}
excel_copilot_health_service_response_time_ms{service="${r}",quantile="max"} ${s.maxResponseTime}
excel_copilot_health_service_response_time_ms{service="${r}",quantile="p95"} ${s.p95ResponseTime}
excel_copilot_health_service_response_time_ms{service="${r}",quantile="p99"} ${s.p99ResponseTime}

# HELP excel_copilot_health_service_checks_total Total number of health checks
# TYPE excel_copilot_health_service_checks_total counter
excel_copilot_health_service_checks_total{service="${r}"} ${s.totalChecks}

`}return t}(t.data);return new Response(e,{status:200,headers:{"Content-Type":"text/plain; charset=utf-8","Cache-Control":"no-cache, no-store, must-revalidate"}})}return l.NextResponse.json(t,{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return l.NextResponse.json({error:"Failed to retrieve health metrics",message:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function h(){return l.NextResponse.json({error:"Method not allowed"},{status:405})}async function n(){return l.NextResponse.json({error:"Method not allowed"},{status:405})}async function p(){return l.NextResponse.json({error:"Method not allowed"},{status:405})}let v=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/health/metrics/route",pathname:"/api/health/metrics",filename:"route",bundlePath:"app/api/health/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\metrics\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:u,serverHooks:x}=v,g="/api/health/metrics/route";function d(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:u})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(400));module.exports=s})();